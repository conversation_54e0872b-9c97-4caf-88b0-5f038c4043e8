#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Synchronous attachment processing for Confluence attachments.
"""

import logging
import os
from typing import Optional, Dict

from .document_extractors import DocumentExtractor
from .enums import ProcessingStatus
from ..config import ProcessingConfig
from ..exceptions import AttachmentProcessingError
from ..models import AttachmentDetail
from ..sync_client import SyncConfluenceClient
from ..thread_pool_manager import get_thread_pool_manager
from ..utils import SecurityValidator


class SyncAttachmentProcessor:
    """Synchronous processor for Confluence attachments."""

    def __init__(
        self,
        client: SyncConfluenceClient,
        processing_config: Optional[ProcessingConfig] = None,
        storage_config=None,
    ):
        """
        Initialize the processor with Confluence client and processing configuration.

        Args:
            client: Synchronous Confluence client for API calls
            processing_config: Processing configuration (optional)
            storage_config: Storage configuration to determine which files to process
        """
        self.client = client
        self.logger = logging.getLogger(__name__)
        self.storage_config = storage_config

        # Use provided configuration or create default
        self.config = processing_config or ProcessingConfig.from_env()

        # Initialize document extractor
        self.extractor = DocumentExtractor(self.logger)

        # Use centralized thread pool manager for CPU-intensive operations
        self.thread_pool_manager = get_thread_pool_manager(
            self.config.thread_pool_config
        )

        # Track processing statistics
        self._stats = {"processed": 0, "failed": 0, "skipped": 0}

        self.logger.info(
            "SyncAttachmentProcessor initialized with centralized thread pool manager"
        )

    def process_attachment(self, attachment: AttachmentDetail) -> AttachmentDetail:
        """
        Process an attachment to extract its text content or mark for raw download.

        Args:
            attachment: Attachment details to process

        Returns:
            AttachmentDetail with extracted text and processing status
        """
        import time

        start_time = time.time()

        try:
            # Validate attachment security
            SecurityValidator.validate_attachment(
                attachment.file_name, attachment.media_type
            )

            # Update processing status
            attachment.processing_status = ProcessingStatus.PROCESSING.value

            # Check if we should process this attachment based on storage config
            if not self._should_process_attachment(attachment):
                self.logger.debug(
                    f"Skipping attachment {attachment.id} - {attachment.file_name} (not in processing list)"
                )
                attachment.processing_status = ProcessingStatus.SKIPPED.value
                attachment.extracted_text = f"[Skipped: {attachment.file_name}]"
                self._stats["skipped"] += 1
                return attachment

            # Check file size limits
            if hasattr(self.storage_config, "max_attachment_size_mb"):
                max_size_bytes = (
                    self.storage_config.max_attachment_size_mb * 1024 * 1024
                )
                if attachment.file_size and attachment.file_size > max_size_bytes:
                    self.logger.warning(
                        f"Attachment {attachment.id} - {attachment.file_name} "
                        f"exceeds size limit ({attachment.file_size} > {max_size_bytes} bytes)"
                    )
                    attachment.processing_status = ProcessingStatus.SKIPPED.value
                    attachment.extracted_text = f"[Too large: {attachment.file_name}]"
                    self._stats["skipped"] += 1
                    return attachment

            # Download attachment
            self.logger.debug(
                f"Downloading attachment {attachment.id} - {attachment.file_name}"
            )
            content_bytes = self.client.download_attachment(attachment)
            download_time = time.time() - start_time
            self.logger.debug(
                f"Download completed for {attachment.id} - {attachment.file_name} "
                f"({len(content_bytes)} bytes in {download_time:.2f}s)"
            )

            # Extract text using thread pool for CPU-intensive operations
            # Submit to document processing thread pool
            future = self.thread_pool_manager.submit_to_pool(
                "document",
                self.extractor.extract_text,
                content_bytes,
                attachment.file_name,
                attachment.media_type,
            )

            # Get the result (this will block until extraction is complete)
            extraction_result = future.result()

            if extraction_result and extraction_result.get("text"):
                attachment.extracted_text = extraction_result["text"]
                attachment.processing_status = ProcessingStatus.SUCCESS.value
                self.logger.debug(
                    f"Text extraction successful for {attachment.id} - {attachment.file_name} "
                    f"({len(attachment.extracted_text)} characters)"
                )
            else:
                attachment.extracted_text = (
                    f"[No text extracted: {attachment.file_name}]"
                )
                attachment.processing_status = ProcessingStatus.NO_TEXT.value
                self.logger.debug(
                    f"No text extracted from {attachment.id} - {attachment.file_name}"
                )

            processing_time = time.time() - start_time
            self.logger.info(
                f"Attachment processing completed for {attachment.id} - {attachment.file_name} "
                f"in {processing_time:.2f}s"
            )

            self._stats["processed"] += 1
            return attachment

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(
                f"Attachment processing failed for {attachment.id} - {attachment.file_name} "
                f"after {processing_time:.2f}s: {e}"
            )

            attachment.processing_status = ProcessingStatus.FAILED.value
            attachment.processing_error = str(e)
            attachment.extracted_text = f"[Processing error: {attachment.file_name}]"
            self._stats["failed"] += 1

            if isinstance(e, AttachmentProcessingError):
                raise
            else:
                raise AttachmentProcessingError(
                    f"Attachment processing failed: {e}",
                    attachment_id=attachment.id,
                    file_name=attachment.file_name,
                )

    def _should_process_attachment(self, attachment: AttachmentDetail) -> bool:
        """
        Determine if an attachment should be processed based on storage configuration.

        Args:
            attachment: Attachment to check

        Returns:
            True if the attachment should be processed, False otherwise
        """
        if not self.storage_config:
            return True

        file_extension = os.path.splitext(attachment.file_name.lower())[1]

        # Check if it's in the list of extensions to convert to text
        if hasattr(self.storage_config, "attachment_extensions_to_convert"):
            if file_extension in self.storage_config.attachment_extensions_to_convert:
                return True

        # Check if it's in the list of extensions to download raw
        if hasattr(self.storage_config, "attachment_extensions_to_download_raw"):
            if (
                file_extension
                in self.storage_config.attachment_extensions_to_download_raw
            ):
                return True

        # Default behavior: process common text-extractable formats
        text_extractable_extensions = [
            ".pdf",
            ".docx",
            ".xlsx",
            ".pptx",
            ".txt",
            ".md",
            ".drawio",
        ]
        return file_extension in text_extractable_extensions

    def get_processing_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        return self._stats.copy()

    def reset_stats(self) -> None:
        """Reset processing statistics."""
        self._stats = {"processed": 0, "failed": 0, "skipped": 0}
