#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Enums et constantes pour le système Confluence Client  processing.
"""

from dataclasses import dataclass
from enum import Enum
from typing import List, Dict, Any, Optional


class ProcessingStatus(Enum):
    """Enumeration pour la gestion de la des valeurs d'état de traitement du document."""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class MediaType(Enum):
    """Enumeration des types de média pris en charge pour le traitement de documents."""

    PDF = "application/pdf"
    DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    TXT = "text/plain"
    HTML = "text/html"
    IMAGE = "image/"
    DRAWIO = "application/vnd.jgraph.mxfile"
    XML = "application/xml"


@dataclass
class ExtractionResult:
    """Résultat de l'opération d'extraction de texte."""

    text: str
    success: bool
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class DrawIOMetadata:
    """Metadaonnées extraites des diagrammes draw.io."""

    title: str = ""
    description: str = ""
    author: str = ""
    created: str = ""
    modified: str = ""
    version: str = ""
    page_count: int = 0
    page_names: List[str] = None
    text_elements: List[str] = None
    shape_count: int = 0
    connector_count: int = 0
    layers: List[str] = None

    def __post_init__(self):
        if self.page_names is None:
            self.page_names = []
        if self.text_elements is None:
            self.text_elements = []
        if self.layers is None:
            self.layers = []
