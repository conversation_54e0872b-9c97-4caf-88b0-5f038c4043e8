#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Classes de configuration pour le système Client Confluence.
"""

import json
import logging
import os
from enum import Enum
from typing import List, Optional

from pydantic import (
    BaseModel,
    Field,
    field_validator,
    model_validator,
    SecretStr,
    HttpUrl,
)

# Importation conditionnelle pour GCS
try:
    from google.cloud import storage
    from google.cloud.exceptions import GoogleCloudError

    GCS_AVAILABLE = True
except ImportError:
    GCS_AVAILABLE = False
    storage = None  # Define 'storage' as None if import fails
    GoogleCloudError = None  # Define 'GoogleCloudError' as None if import fails


class Environment(str, Enum):
    """Enumération des environnements disponibles."""

    DEV = "dev"
    STAGING = "pprod"
    PROD = "prod"


class RetryConfig(BaseModel):
    """Configuration pour les mécanismes de retry."""

    model_config = {"revalidate_instances": "always"}

    # Interface de test - champs principaux
    max_attempts: int = 3  # Nombre maximum de tentatives
    base_delay: float = 1.0  # Délai de base en secondes
    max_delay: float = 30.0  # Délai maximum en secondes
    exponential_base: float = 2.0  # Base pour le backoff exponentiel

    # Champs de compatibilité avec l'ancienne interface
    max_retries: int = 3  # Nombre maximum de tentatives (alias)
    initial_backoff: float = 1.0  # Délai initial en secondes (alias)
    max_backoff: float = 60.0  # Délai maximum en secondes (alias)
    backoff_factor: float = (
        2.0  # Facteur de multiplication pour le backoff exponentiel (alias)
    )
    jitter: bool = (
        True  # Ajouter un facteur aléatoire pour éviter les tempêtes de requêtes
    )
    retry_on_status_codes: List[int] = Field(
        default_factory=lambda: [429, 500, 502, 503, 504]
    )  # Codes HTTP à réessayer

    @field_validator("max_attempts")
    @classmethod
    def validate_max_attempts(cls, v: int) -> int:
        """Valide que max_attempts est positif."""
        if v <= 0:
            raise ValueError("max_attempts doit être positif")
        return v

    @field_validator("base_delay")
    @classmethod
    def validate_base_delay(cls, v: float) -> float:
        """Valide que base_delay est positif."""
        if v < 0:
            raise ValueError("base_delay ne peut pas être négatif")
        return v

    @classmethod
    def from_env(cls, prefix: str = "") -> "RetryConfig":
        """Crée une configuration de retry à partir des variables d'environnement."""
        return cls(
            max_attempts=int(os.getenv(f"{prefix}RETRY_MAX_ATTEMPTS", "3")),
            base_delay=float(os.getenv(f"{prefix}RETRY_BASE_DELAY", "1.0")),
            max_delay=float(os.getenv(f"{prefix}RETRY_MAX_DELAY", "30.0")),
            exponential_base=float(os.getenv(f"{prefix}RETRY_EXPONENTIAL_BASE", "2.0")),
            max_retries=int(os.getenv(f"{prefix}RETRY_MAX_ATTEMPTS", "3")),
            initial_backoff=float(os.getenv(f"{prefix}RETRY_INITIAL_BACKOFF", "1.0")),
            max_backoff=float(os.getenv(f"{prefix}RETRY_MAX_BACKOFF", "60.0")),
            backoff_factor=float(os.getenv(f"{prefix}RETRY_BACKOFF_FACTOR", "2.0")),
            jitter=os.getenv(f"{prefix}RETRY_JITTER", "true").lower() == "true",
            retry_on_status_codes=[
                int(code)
                for code in os.getenv(
                    f"{prefix}RETRY_STATUS_CODES", "429,500,502,503,504"
                ).split(",")
            ],
        )


class CircuitBreakerConfig(BaseModel):
    """Configuration pour le Circuit Breaker Pattern."""

    model_config = {"revalidate_instances": "always"}

    # Interface de test - champs principaux
    failure_threshold: int = 5  # Nombre d'échecs consécutifs avant d'ouvrir le circuit
    recovery_timeout: float = (
        60.0  # Temps en secondes avant de passer en état semi-ouvert
    )
    expected_exception: type = Exception  # Type d'exception attendu

    # Champs de compatibilité avec l'ancienne interface
    reset_timeout: float = (
        60.0  # Temps en secondes avant de passer en état semi-ouvert (alias)
    )
    reset_threshold: int = 2  # Nombre de succès consécutifs pour fermer le circuit
    enabled: bool = True  # Activer/désactiver le Circuit Breaker

    @classmethod
    def from_env(cls, prefix: str = "") -> "CircuitBreakerConfig":
        """Crée une configuration de Circuit Breaker à partir des variables d'environnement."""
        return cls(
            failure_threshold=int(
                os.getenv(f"{prefix}CIRCUIT_BREAKER_FAILURE_THRESHOLD", "5")
            ),
            recovery_timeout=float(
                os.getenv(f"{prefix}CIRCUIT_BREAKER_RECOVERY_TIMEOUT", "60.0")
            ),
            expected_exception=Exception,
            reset_timeout=float(
                os.getenv(f"{prefix}CIRCUIT_BREAKER_RESET_TIMEOUT", "60.0")
            ),
            reset_threshold=int(
                os.getenv(f"{prefix}CIRCUIT_BREAKER_RESET_THRESHOLD", "2")
            ),
            enabled=os.getenv(f"{prefix}CIRCUIT_BREAKER_ENABLED", "true").lower()
            == "true",
        )


class ConfluenceConfig(BaseModel):
    """Configuration pour la connexion à Confluence."""

    model_config = {"revalidate_instances": "always"}

    url: HttpUrl
    username: Optional[str] = None  # Optionnel avec PAT
    api_token: Optional[SecretStr] = None  # Token API classique (obsolète)
    pat_token: Optional[SecretStr] = None  # Personal Access Token (PAT)
    default_space_key: str = "EXAMPLE"
    timeout: int = 30
    retry_config: RetryConfig = Field(default_factory=RetryConfig)
    circuit_breaker_config: CircuitBreakerConfig = Field(
        default_factory=CircuitBreakerConfig
    )

    # Configuration de la pagination parallèle
    enable_parallel_pagination: bool = True
    max_parallel_requests: int = 3
    parallel_pagination_threshold: int = (
        200  # Seuil minimum pour activer la pagination parallèle
    )

    @model_validator(mode="after")
    def validate_token(self) -> "ConfluenceConfig":
        """Valide qu'au moins un type de token est fourni."""
        if not self.pat_token and not self.api_token:
            raise ValueError("Vous devez fournir soit un PAT token, soit un API token")
        return self

    @classmethod
    def from_env(cls, prefix: str = "") -> "ConfluenceConfig":
        """Crée une configuration Confluence à partir des variables d'environnement."""
        config_params = {
            "url": os.getenv(f"{prefix}CONFLUENCE_URL", ""),
            "default_space_key": os.getenv(f"{prefix}DEFAULT_SPACE_KEY", "EXAMPLE"),
            "timeout": int(os.getenv(f"{prefix}CONFLUENCE_TIMEOUT", "30")),
            "retry_config": RetryConfig.from_env(prefix),
            "circuit_breaker_config": CircuitBreakerConfig.from_env(prefix),
            "enable_parallel_pagination": os.getenv(
                f"{prefix}ENABLE_PARALLEL_PAGINATION", "true"
            ).lower()
            == "true",
            "max_parallel_requests": int(
                os.getenv(f"{prefix}MAX_PARALLEL_REQUESTS", "3")
            ),
            "parallel_pagination_threshold": int(
                os.getenv(f"{prefix}PARALLEL_PAGINATION_THRESHOLD", "200")
            ),
        }

        # Configurer l'authentification
        if os.getenv(f"{prefix}CONFLUENCE_PAT_TOKEN"):
            config_params["pat_token"] = os.getenv(f"{prefix}CONFLUENCE_PAT_TOKEN")
        elif os.getenv(f"{prefix}CONFLUENCE_API_TOKEN") and os.getenv(
            f"{prefix}CONFLUENCE_USERNAME"
        ):
            config_params["api_token"] = os.getenv(f"{prefix}CONFLUENCE_API_TOKEN")
            config_params["username"] = os.getenv(f"{prefix}CONFLUENCE_USERNAME")

        return cls(**config_params)


class ThreadPoolConfig(BaseModel):
    """Configuration pour les pools de threads."""

    model_config = {"revalidate_instances": "always"}

    # Interface de test - champs principaux
    max_workers: int = 4  # Nombre maximum de workers
    thread_name_prefix: str = "ConfluenceRAG-Worker"  # Préfixe pour les noms de threads
    enable_monitoring: bool = False  # Activer le monitoring des threads

    # Champs de compatibilité avec l'ancienne interface
    io_thread_workers: int = (
        8  # Threads pour les opérations I/O (lecture/écriture fichiers)
    )
    document_processing_workers: int = (
        4  # Threads pour l'extraction de texte des documents
    )
    api_thread_workers: int = (
        3  # Threads pour les appels API synchrones (atlassian-python-api)
    )
    max_queue_size: int = 100  # Taille maximale de la queue

    @field_validator("max_workers")
    @classmethod
    def validate_max_workers(cls, v: int) -> int:
        """Valide que max_workers est positif."""
        if v <= 0:
            raise ValueError("max_workers doit être positif")
        return v

    @classmethod
    def from_env(cls, prefix: str = "") -> "ThreadPoolConfig":
        """Crée une configuration de pool de threads à partir des variables d'environnement."""
        return cls(
            max_workers=int(os.getenv(f"{prefix}MAX_WORKERS", "4")),
            thread_name_prefix=os.getenv(
                f"{prefix}THREAD_NAME_PREFIX", "ConfluenceRAG-Worker"
            ),
            enable_monitoring=os.getenv(f"{prefix}ENABLE_MONITORING", "false").lower()
            == "true",
            io_thread_workers=int(os.getenv(f"{prefix}IO_THREAD_WORKERS", "8")),
            document_processing_workers=int(
                os.getenv(f"{prefix}DOCUMENT_PROCESSING_WORKERS", "4")
            ),
            api_thread_workers=int(os.getenv(f"{prefix}API_THREAD_WORKERS", "3")),
            max_queue_size=int(os.getenv(f"{prefix}MAX_QUEUE_SIZE", "100")),
        )


class LoggingConfig(BaseModel):
    """Configuration pour le système de logging."""

    model_config = {"revalidate_instances": "always"}

    # Niveau de log
    level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

    # Configuration des handlers
    enable_console: bool = True  # Activer la sortie console
    enable_file: bool = True  # Activer la sortie fichier

    # Configuration du fichier de log
    file_path: str = "confluence_rag.log"  # Chemin du fichier de log
    max_file_size_mb: int = 10  # Taille maximale du fichier en MB
    backup_count: int = 5  # Nombre de fichiers de sauvegarde
    file_encoding: str = "utf-8"  # Encodage du fichier

    # Format des logs
    structured: bool = True  # Utiliser le format JSON structuré
    include_traceback: bool = True  # Inclure la trace d'appel pour les erreurs

    # Sécurité
    enable_security_filter: bool = True  # Activer le filtre de sécurité
    enable_correlation_id: bool = True  # Activer les identifiants de corrélation

    # Configuration des loggers externes
    suppress_external_loggers: bool = True  # Réduire le niveau des loggers externes
    external_logger_level: str = "WARNING"  # Niveau pour les loggers externes

    @field_validator("level", "external_logger_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Valide que le niveau de log est valide."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(
                f"Niveau de log invalide: {v}. Niveaux valides: {valid_levels}"
            )
        return v.upper()

    @field_validator("max_file_size_mb")
    @classmethod
    def validate_file_size(cls, v: int) -> int:
        """Valide que la taille du fichier est positive."""
        if v <= 0:
            raise ValueError("La taille maximale du fichier doit être positive")
        return v

    @field_validator("backup_count")
    @classmethod
    def validate_backup_count(cls, v: int) -> int:
        """Valide que le nombre de sauvegardes est positif."""
        if v < 0:
            raise ValueError("Le nombre de sauvegardes ne peut pas être négatif")
        return v

    @classmethod
    def from_env(cls, prefix: str = "") -> "LoggingConfig":
        """Crée une configuration de logging à partir des variables d'environnement."""
        return cls(
            level=os.getenv(f"{prefix}LOG_LEVEL", "INFO"),
            enable_console=os.getenv(f"{prefix}LOG_ENABLE_CONSOLE", "true").lower()
            == "true",
            enable_file=os.getenv(f"{prefix}LOG_ENABLE_FILE", "true").lower() == "true",
            file_path=os.getenv(f"{prefix}LOG_FILE", "confluence_rag.log"),
            max_file_size_mb=int(os.getenv(f"{prefix}LOG_MAX_FILE_SIZE_MB", "10")),
            backup_count=int(os.getenv(f"{prefix}LOG_BACKUP_COUNT", "5")),
            file_encoding=os.getenv(f"{prefix}LOG_FILE_ENCODING", "utf-8"),
            structured=os.getenv(f"{prefix}STRUCTURED_LOGGING", "true").lower()
            == "true",
            include_traceback=os.getenv(
                f"{prefix}LOG_INCLUDE_TRACEBACK", "true"
            ).lower()
            == "true",
            enable_security_filter=os.getenv(f"{prefix}SECURE_LOGGING", "true").lower()
            == "true",
            enable_correlation_id=os.getenv(
                f"{prefix}LOG_ENABLE_CORRELATION_ID", "true"
            ).lower()
            == "true",
            suppress_external_loggers=os.getenv(
                f"{prefix}LOG_SUPPRESS_EXTERNAL", "true"
            ).lower()
            == "true",
            external_logger_level=os.getenv(f"{prefix}LOG_EXTERNAL_LEVEL", "WARNING"),
        )


class HealthCheckConfig(BaseModel):
    """Configuration pour les health checks."""

    model_config = {"revalidate_instances": "always"}

    enabled: bool = True
    check_interval_seconds: int = 30
    timeout_seconds: int = 10

    # Seuils d'alerte
    memory_threshold_percent: float = 85.0
    disk_threshold_percent: float = 90.0
    error_rate_threshold_percent: float = 10.0

    # Configuration des checks spécifiques
    check_confluence_api: bool = True
    check_storage: bool = True
    check_circuit_breakers: bool = True
    check_thread_pools: bool = True
    check_system_resources: bool = True

    # Endpoints de health check
    health_endpoint: str = "/health"
    readiness_endpoint: str = "/ready"
    liveness_endpoint: str = "/live"

    @classmethod
    def from_env(cls, prefix: str = "") -> "HealthCheckConfig":
        """Charge la configuration des health checks depuis les variables d'environnement."""
        return cls(
            enabled=os.getenv(f"{prefix}HEALTH_CHECK_ENABLED", "true").lower()
            == "true",
            check_interval_seconds=int(
                os.getenv(f"{prefix}HEALTH_CHECK_INTERVAL", "30")
            ),
            timeout_seconds=int(os.getenv(f"{prefix}HEALTH_CHECK_TIMEOUT", "10")),
            memory_threshold_percent=float(
                os.getenv(f"{prefix}HEALTH_MEMORY_THRESHOLD", "85.0")
            ),
            disk_threshold_percent=float(
                os.getenv(f"{prefix}HEALTH_DISK_THRESHOLD", "90.0")
            ),
            error_rate_threshold_percent=float(
                os.getenv(f"{prefix}HEALTH_ERROR_RATE_THRESHOLD", "10.0")
            ),
            check_confluence_api=os.getenv(
                f"{prefix}HEALTH_CHECK_CONFLUENCE", "true"
            ).lower()
            == "true",
            check_storage=os.getenv(f"{prefix}HEALTH_CHECK_STORAGE", "true").lower()
            == "true",
            check_circuit_breakers=os.getenv(
                f"{prefix}HEALTH_CHECK_CIRCUIT_BREAKERS", "true"
            ).lower()
            == "true",
            check_thread_pools=os.getenv(
                f"{prefix}HEALTH_CHECK_THREAD_POOLS", "true"
            ).lower()
            == "true",
            check_system_resources=os.getenv(
                f"{prefix}HEALTH_CHECK_SYSTEM_RESOURCES", "true"
            ).lower()
            == "true",
            health_endpoint=os.getenv(f"{prefix}HEALTH_ENDPOINT", "/health"),
            readiness_endpoint=os.getenv(f"{prefix}READINESS_ENDPOINT", "/ready"),
            liveness_endpoint=os.getenv(f"{prefix}LIVENESS_ENDPOINT", "/live"),
        )


class SearchCriteria(BaseModel):
    """Critères de recherche pour Confluence."""

    model_config = {"revalidate_instances": "always"}

    spaces: List[str] = Field(default_factory=list)  # Espaces à rechercher
    labels: List[str] = Field(default_factory=list)  # Étiquettes à rechercher
    types: List[str] = Field(
        default_factory=lambda: ["page", "blogpost"]
    )  # Types de contenu
    content_types: List[str] = Field(
        default_factory=lambda: ["page"]
    )  # Types de contenu (interface test)
    max_results: int = 1000  # Nombre maximum de résultats
    include_archived: bool = False  # Inclure le contenu archivé
    include_attachments: bool = True  # Inclure les pièces jointes
    include_children: bool = False  # Inclure les pages enfants
    last_modified_days: Optional[int] = (
        None  # Nombre de jours depuis la dernière modification
    )

    @field_validator("max_results")
    @classmethod
    def validate_max_results(cls, v: int) -> int:
        """Valide que max_results est positif et inférieur à 10001."""
        if v <= 0:
            raise ValueError("max_results doit être positif")
        if v > 10000:
            raise ValueError("max_results ne peut pas dépasser 10000")
        return v

    @field_validator("last_modified_days")
    @classmethod
    def validate_last_modified_days(cls, v: Optional[int]) -> Optional[int]:
        """Valide que last_modified_days est positif si fourni."""
        if v is not None and v <= 0:
            raise ValueError("last_modified_days doit être positif")
        return v

    @model_validator(mode="after")
    def validate_criteria(self) -> "SearchCriteria":
        """Valide les critères de recherche."""
        # Synchroniser types et content_types
        if self.content_types != ["page"] and self.types == ["page", "blogpost"]:
            self.types = self.content_types
        elif self.types != ["page", "blogpost"] and self.content_types == ["page"]:
            self.content_types = self.types

        return self

    def to_cql(self) -> str:
        """Convertit les critères en requête CQL."""
        conditions = []

        # Espaces
        if self.spaces:
            space_conditions = " OR ".join(
                [f'space = "{space}"' for space in self.spaces]
            )
            conditions.append(f"({space_conditions})")

        # Types de contenu
        if self.types:
            type_conditions = " OR ".join(
                [f'type = "{content_type}"' for content_type in self.types]
            )
            conditions.append(f"({type_conditions})")

        # Étiquettes
        if self.labels:
            label_conditions = " AND ".join(
                [f'label = "{label}"' for label in self.labels]
            )
            conditions.append(f"({label_conditions})")

        # Contenu archivé
        if not self.include_archived:
            conditions.append("status = current")  # Ou "status != archived"

        # Filtrer par dernière modification
        if self.last_modified_days is not None and self.last_modified_days > 0:
            conditions.append(f"lastModified >= now('-{self.last_modified_days}d')")

        return " AND ".join(conditions) if conditions else "type = page"

    @classmethod
    def from_file(cls, file_path: str, gcs_bucket: str = None) -> "SearchCriteria":
        """Charge les critères depuis un fichier JSON."""
        if gcs_bucket and GCS_AVAILABLE:
            # Charger depuis GCS
            try:
                client = storage.Client()
                bucket = client.bucket(gcs_bucket)
                blob = bucket.blob(file_path)
                content = blob.download_as_text()
                data = json.loads(content)
                return cls(**data)
            except Exception as e:
                logging.error(f"Erreur lors du chargement depuis GCS: {e}")
                raise
        else:
            # Charger depuis le système de fichiers local
            if os.path.exists(file_path):
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                return cls(**data)
            else:
                logging.warning(
                    f"Fichier de critères non trouvé: {file_path}. Utilisation des critères par défaut."
                )
                return cls()


class StorageConfig(BaseModel):
    """Configuration pour le stockage des données."""

    model_config = {"revalidate_instances": "always"}

    type: str = "filesystem"  # "filesystem" ou "gcs"
    base_dir: str = "output_data_dir"  # Répertoire de base pour filesystem
    bucket_name: Optional[str] = None  # Nom du bucket GCS
    base_prefix: str = "confluence_rag"  # Préfixe de base pour GCS

    # Champs de compatibilité avec l'ancienne interface
    storage_type: Optional[str] = None  # Alias pour type
    output_dir: Optional[str] = None  # Alias pour base_dir
    gcs_bucket_name: Optional[str] = None  # Alias pour bucket_name
    gcs_base_prefix: Optional[str] = None  # Alias pour base_prefix

    # Configuration des pièces jointes (conservée pour compatibilité)
    include_attachments: bool = True  # Inclure les pièces jointes
    attachment_extensions_to_convert: List[str] = Field(
        default_factory=lambda: [".drawio"]
    )  # Extensions à convertir en texte
    attachment_extensions_to_download_raw: List[str] = Field(
        default_factory=lambda: [
            ".pdf",
            ".xlsx",
            ".docx",
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".bmp",
            ".tiff",
            ".svg",
            ".txt",
            ".md",
        ]
    )  # Extensions à télécharger en tant que fichiers bruts
    max_attachment_size_mb: int = 50  # Taille maximale des pièces jointes en MB

    @field_validator("type")
    @classmethod
    def validate_type(cls, v: str) -> str:
        """Valide le type de stockage."""
        valid_types = ["filesystem", "gcs"]
        if v not in valid_types:
            raise ValueError(
                f"Type de stockage invalide: {v}. Types valides: {valid_types}"
            )
        return v

    @field_validator("max_attachment_size_mb")
    @classmethod
    def validate_attachment_size(cls, v: int) -> int:
        """Valide la taille maximale des pièces jointes."""
        if v <= 0:
            raise ValueError("La taille maximale des pièces jointes doit être positive")
        return v

    @model_validator(mode="after")
    def validate_gcs_config(self) -> "StorageConfig":
        """Valide la configuration GCS et synchronise les alias."""
        # Synchroniser les champs alias avec les champs principaux
        if self.storage_type and not hasattr(self, "_type_set"):
            self.type = self.storage_type
        if self.output_dir and not hasattr(self, "_base_dir_set"):
            self.base_dir = self.output_dir
        if self.gcs_bucket_name and not hasattr(self, "_bucket_name_set"):
            self.bucket_name = self.gcs_bucket_name
        if self.gcs_base_prefix and not hasattr(self, "_base_prefix_set"):
            self.base_prefix = self.gcs_base_prefix

        # Valider la configuration GCS
        if self.type == "gcs" and not self.bucket_name:
            raise ValueError("bucket_name est requis quand type='gcs'")
        return self

    @classmethod
    def from_env(cls, prefix: str = "") -> "StorageConfig":
        """Crée une configuration de stockage à partir des variables d'environnement."""
        return cls(
            type=os.getenv(f"{prefix}STORAGE_TYPE", "filesystem"),
            base_dir=os.getenv(f"{prefix}OUTPUT_DIR", "output_data_dir"),
            bucket_name=os.getenv(f"{prefix}GCS_BUCKET_NAME"),
            base_prefix=os.getenv(f"{prefix}GCS_BASE_PREFIX", "confluence_rag"),
            include_attachments=os.getenv(
                f"{prefix}INCLUDE_ATTACHMENTS", "true"
            ).lower()
            == "true",
            attachment_extensions_to_convert=os.getenv(
                f"{prefix}ATTACHMENT_EXTENSIONS_TO_CONVERT", ".txt,.md"
            ).split(","),
            attachment_extensions_to_download_raw=os.getenv(
                f"{prefix}ATTACHMENT_EXTENSIONS_TO_DOWNLOAD_RAW",
                ".pdf,.xlsx,.docx,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.svg",
            ).split(","),
            max_attachment_size_mb=int(
                os.getenv(f"{prefix}MAX_ATTACHMENT_SIZE_MB", "50")
            ),
        )


class ProcessingConfig(BaseModel):
    """Configuration pour le traitement des contenus."""

    model_config = {"revalidate_instances": "always"}

    chunk_size: int = 1000  # Taille des chunks de texte
    overlap_size: int = 200  # Taille de chevauchement entre chunks
    max_parallel_downloads: int = 5  # Nombre maximum de téléchargements parallèles
    max_thread_workers: int = 5  # Nombre maximum de workers de thread
    enable_change_tracking: bool = (
        True  # Activer/désactiver le suivi des changements par hash
    )
    thread_pool_config: Optional[ThreadPoolConfig] = Field(
        default_factory=ThreadPoolConfig
    )  # Configuration des pools de threads

    @field_validator(
        "chunk_size", "overlap_size", "max_parallel_downloads", "max_thread_workers"
    )
    @classmethod
    def validate_positive_int(cls, v: int) -> int:
        """Valide que la valeur est positive."""
        if v <= 0:
            raise ValueError("La valeur doit être positive")
        return v

    @model_validator(mode="after")
    def validate_overlap(self) -> "ProcessingConfig":
        """Valide que l'overlap est inférieur à la taille du chunk."""
        if self.overlap_size >= self.chunk_size:
            raise ValueError("overlap_size doit être inférieur à chunk_size")
        return self

    @classmethod
    def from_env(cls, prefix: str = "") -> "ProcessingConfig":
        """Crée une configuration de traitement à partir des variables d'environnement."""
        return cls(
            chunk_size=int(os.getenv(f"{prefix}CHUNK_SIZE", "1000")),
            overlap_size=int(os.getenv(f"{prefix}OVERLAP_SIZE", "200")),
            max_parallel_downloads=int(
                os.getenv(f"{prefix}MAX_PARALLEL_DOWNLOADS", "5")
            ),
            max_thread_workers=int(os.getenv(f"{prefix}MAX_THREAD_WORKERS", "5")),
            enable_change_tracking=os.getenv(
                f"{prefix}ENABLE_CHANGE_TRACKING", "true"
            ).lower()
            in ("true", "1", "yes"),
            thread_pool_config=ThreadPoolConfig.from_env(prefix),
        )


class EnvironmentConfig(BaseModel):
    """Configuration pour la gestion des environnements."""

    active_environment: Environment = Environment.DEV
    config_file_path: Optional[str] = None

    @classmethod
    def from_env(cls) -> "EnvironmentConfig":
        """Charge la configuration d'environnement depuis les variables d'environnement."""
        env_name = os.getenv("CONFLUENCE_RAG_ENVIRONMENT", Environment.DEV.value)

        # Validation de l'environnement
        try:
            active_env = Environment(env_name.lower())
        except ValueError:
            logging.warning(
                f"Environnement inconnu '{env_name}'. "
                f"Utilisation de l'environnement par défaut: {Environment.DEV.value}"
                )
            active_env = Environment.DEV

        return cls(
            active_environment=active_env,
            config_file_path=os.getenv("CONFLUENCE_RAG_CONFIG_FILE"),
        )


class AppConfig(BaseModel):
    """Configuration principale de l'application."""

    confluence: ConfluenceConfig
    thread_pool: ThreadPoolConfig = Field(default_factory=ThreadPoolConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    health_check: HealthCheckConfig = Field(default_factory=HealthCheckConfig)

    # Paramètres supplémentaires selon l'environnement
    debug_mode: bool = False
    enable_profiling: bool = False
    enable_metrics: bool = False

    @classmethod
    def for_environment(cls, environment: Environment) -> "AppConfig":
        """Charge la configuration pour un environnement spécifique."""
        prefix = f"{environment.value.upper()}_"

        # Configuration spécifique par environnement
        if environment == Environment.DEV:
            # Configuration de développement - logs détaillés
            logging_config = LoggingConfig.from_env(prefix)
            logging_config.level = "DEBUG"
            logging_config.structured = False  # Format texte plus lisible en dev
            logging_config.enable_console = True
            logging_config.enable_file = True
            logging_config.suppress_external_loggers = (
                False  # Voir tous les logs en dev
            )

            return cls(
                confluence=ConfluenceConfig.from_env(prefix),
                thread_pool=ThreadPoolConfig.from_env(prefix),
                logging=logging_config,
                health_check=HealthCheckConfig.from_env(prefix),
                debug_mode=True,
                enable_profiling=True,
                enable_metrics=False,
            )
        elif environment == Environment.STAGING:
            # Configuration de staging - logs structurés pour analyse
            logging_config = LoggingConfig.from_env(prefix)
            logging_config.level = "INFO"
            logging_config.structured = True
            logging_config.enable_console = True
            logging_config.enable_file = True
            logging_config.max_file_size_mb = 20  # Fichiers plus gros en staging
            logging_config.backup_count = 10

            return cls(
                confluence=ConfluenceConfig.from_env(prefix),
                thread_pool=ThreadPoolConfig.from_env(prefix),
                logging=logging_config,
                health_check=HealthCheckConfig.from_env(prefix),
                debug_mode=False,
                enable_profiling=True,
                enable_metrics=True,
            )
        elif environment == Environment.PROD:
            # Configuration de production - logs optimisés et sécurisés
            logging_config = LoggingConfig.from_env(prefix)
            logging_config.level = "WARNING"
            logging_config.structured = True
            logging_config.enable_console = False  # Pas de console en prod
            logging_config.enable_file = True
            logging_config.max_file_size_mb = 50  # Gros fichiers en prod
            logging_config.backup_count = 20
            logging_config.enable_security_filter = True  # Sécurité renforcée

            return cls(
                confluence=ConfluenceConfig.from_env(prefix),
                thread_pool=ThreadPoolConfig.from_env(prefix),
                logging=logging_config,
                health_check=HealthCheckConfig.from_env(prefix),
                debug_mode=False,
                enable_profiling=False,
                enable_metrics=True,
            )
        else:
            raise ValueError(f"Environnement non supporté: {environment}")

    @classmethod
    def load(cls) -> "AppConfig":
        """Charge la configuration appropriée pour l'environnement actif et valide la configuration."""
        env_config = EnvironmentConfig.from_env()

        # Si un fichier de configuration est spécifié, l'utiliser
        if env_config.config_file_path and os.path.exists(env_config.config_file_path):
            try:
                with open(env_config.config_file_path, "r") as f:
                    config_data = json.load(f)
                # Créer la configuration à partir du fichier
                config = cls.model_validate(config_data)
            except Exception as e:
                logging.error(
                    f"Erreur lors du chargement du fichier de configuration: {e}"
                )
                logging.warning(
                    "Utilisation de la configuration par défaut pour l'environnement"
                )
                config = cls.for_environment(env_config.active_environment)
        else:
            # Sinon, utiliser les variables d'environnement
            config = cls.for_environment(env_config.active_environment)

        # Valider la configuration système
        errors = ConfigValidator.validate_app_config(config)
        if errors:
            error_msg = "Erreurs de configuration système détectées: " + "; ".join(
                errors
            )
            logging.error(error_msg)
            raise ValueError(error_msg)

        return config


# Fonction pratique pour obtenir la configuration active
def get_config() -> AppConfig:
    """Récupère la configuration appropriée pour l'environnement actif."""
    return AppConfig.load()


class ConfigValidator(BaseModel):
    """Validateur de configuration système pour vérifier que tous les services requis sont correctement configurés."""

    @staticmethod
    def validate_confluence_config(config: ConfluenceConfig) -> List[str]:
        """Valide la configuration Confluence et retourne les erreurs éventuelles."""
        errors = []

        # Vérifier l'URL
        if not config.url:
            errors.append("L'URL Confluence n'est pas configurée")

        # Vérifier l'authentification
        if not config.pat_token and not (config.api_token and config.username):
            errors.append(
                "Aucune méthode d'authentification Confluence n'est configurée"
            )

        # Vérifier les paramètres de pagination parallèle
        if config.enable_parallel_pagination and config.max_parallel_requests <= 0:
            errors.append("Le nombre maximum de requêtes parallèles doit être positif")

        return errors

    @staticmethod
    def validate_thread_pool_config(config: ThreadPoolConfig) -> List[str]:
        """Valide la configuration des pools de threads et retourne les erreurs éventuelles."""
        errors = []

        # Vérifier les valeurs des workers
        if config.io_thread_workers <= 0:
            errors.append("Le nombre de workers I/O doit être positif")

        if config.document_processing_workers <= 0:
            errors.append(
                "Le nombre de workers de traitement de documents doit être positif"
            )

        if config.api_thread_workers <= 0:
            errors.append("Le nombre de workers API doit être positif")

        return errors

    @staticmethod
    def validate_logging_config(config: LoggingConfig) -> List[str]:
        """Valide la configuration de logging et retourne les erreurs éventuelles."""
        errors = []

        # Vérifier qu'au moins un handler est activé
        if not config.enable_console and not config.enable_file:
            errors.append(
                "Au moins un handler de logging (console ou fichier) doit être activé"
            )

        # Vérifier le chemin du fichier si le handler fichier est activé
        if config.enable_file:
            try:
                # Vérifier que le répertoire parent existe ou peut être créé
                file_dir = os.path.dirname(os.path.abspath(config.file_path))
                if not os.path.exists(file_dir):
                    try:
                        os.makedirs(file_dir, exist_ok=True)
                    except OSError as e:
                        errors.append(
                            f"Impossible de créer le répertoire pour le fichier de log: {e}"
                        )
            except Exception as e:
                errors.append(f"Chemin de fichier de log invalide: {e}")

        # Vérifier l'encodage
        try:
            "test".encode(config.file_encoding)
        except LookupError:
            errors.append(f"Encodage de fichier invalide: {config.file_encoding}")

        return errors

    @staticmethod
    def validate_storage_config(config: StorageConfig) -> List[str]:
        """Valide la configuration de stockage et retourne les erreurs éventuelles."""
        errors = []

        # Vérifier la configuration GCS si nécessaire
        if config.type == "gcs":
            if not config.bucket_name:
                errors.append("bucket_name est requis quand type='gcs'")
            if not GCS_AVAILABLE:
                errors.append(
                    "Les bibliothèques Google Cloud Storage ne sont pas disponibles"
                )

        # Vérifier le répertoire de sortie pour filesystem
        if config.type == "filesystem":
            try:
                os.makedirs(config.base_dir, exist_ok=True)
            except OSError as e:
                errors.append(f"Impossible de créer le répertoire de sortie: {e}")

        return errors

    @staticmethod
    def validate_processing_config(config: ProcessingConfig) -> List[str]:
        """Valide la configuration de traitement et retourne les erreurs éventuelles."""
        errors = []

        # Vérifier que l'overlap est cohérent avec la taille du chunk
        if config.overlap_size >= config.chunk_size:
            errors.append("overlap_size doit être inférieur à chunk_size")

        return errors

    @staticmethod
    def validate_app_config(config: "AppConfig") -> List[str]:
        """Valide la configuration complète de l'application."""
        errors = []

        # Valider la configuration Confluence
        errors.extend(ConfigValidator.validate_confluence_config(config.confluence))

        # Valider la configuration des pools de threads
        errors.extend(ConfigValidator.validate_thread_pool_config(config.thread_pool))

        # Valider la configuration de logging
        errors.extend(ConfigValidator.validate_logging_config(config.logging))

        return errors


def validate_system_configuration() -> bool:
    """
    Valide la configuration système complète et affiche les erreurs.

    Returns:
        bool: True si la configuration est valide, False sinon.
    """
    logger = logging.getLogger(__name__)
    logger.info("Validation de la configuration système...")

    try:
        # Charger la configuration
        config = get_config()

        # Valider la configuration
        errors = ConfigValidator.validate_app_config(config)

        if errors:
            logger.error("Erreurs de configuration détectées:")
            for error in errors:
                logger.error(f"  - {error}")
            return False

        logger.info("Configuration système validée avec succès")
        return True

    except Exception as e:
        logger.error(f"Erreur lors de la validation de la configuration: {e}")
        return False
