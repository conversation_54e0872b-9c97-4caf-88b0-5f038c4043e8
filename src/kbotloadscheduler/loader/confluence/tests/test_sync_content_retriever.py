#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le récupérateur de contenu synchrone.
"""

import unittest
from datetime import datetime
from unittest.mock import Mock, patch

from pydantic import SecretStr

from ..config import ConfluenceConfig, SearchCriteria, ProcessingConfig
from ..exceptions import ContentProcessingError
from ..models import ContentItem, AttachmentDetail, UserInfo, SpaceInfo
from ..processing.sync_content_retriever import SyncContentRetriever
from ..sync_client import SyncConfluenceClient


class TestSyncContentRetriever(unittest.TestCase):
    """Tests pour SyncContentRetriever."""

    def setUp(self):
        """Configuration des tests."""
        # Configuration
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net", pat_token=SecretStr("test_token")
        )

        self.processing_config = ProcessingConfig(
            chunk_size=1000,
            overlap_size=200,
            max_parallel_downloads=3,
            max_thread_workers=2,
        )

        # Mock client
        self.mock_client = Mock(spec=SyncConfluenceClient)

        # Données de test
        self.test_user = UserInfo(
            id="user123", username="testuser", display_name="Test User"
        )

        self.test_space = SpaceInfo(
            id="space123", key="TEST", name="Test Space", type="global"
        )

        self.test_content_item = ContentItem(
            id="123",
            type="page",
            status="current",
            title="Test Page",
            space=self.test_space,
            version={"number": 1},
            created=datetime.now(),
            creator=self.test_user,
            last_updated=datetime.now(),
            last_updater=self.test_user,
            content_url="https://test.atlassian.net/wiki/rest/api/content/123",
            web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/123",
            body_storage="<p>Test content</p>",
            body_view="<p>Test content</p>",
            body_plain="Test content",
        )

        self.test_attachment = AttachmentDetail(
            id="att123",
            title="test.pdf",
            file_name="test.pdf",
            file_size=1024,
            media_type="application/pdf",
            download_url="https://test.atlassian.net/download/test.pdf",
            created=datetime.now(),
            last_updated=datetime.now(),
            creator=self.test_user,
            content_id="123",
        )

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager"
    )
    def test_content_retriever_initialization(
        self, mock_thread_pool, mock_attachment_processor_class
    ):
        """Test de l'initialisation du récupérateur de contenu."""
        # Setup mocks
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor
        mock_thread_pool.return_value = Mock()

        retriever = SyncContentRetriever(self.mock_client, self.processing_config)

        self.assertEqual(retriever.client, self.mock_client)
        self.assertEqual(retriever.config, self.processing_config)
        self.assertIsNotNone(retriever.attachment_processor)
        self.assertIsNotNone(retriever.chunker)
        self.assertIsNotNone(retriever.thread_pool_manager)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.ProcessingConfig.from_env"
    )
    def test_content_retriever_default_config(
        self, mock_from_env, mock_thread_pool, mock_attachment_processor_class
    ):
        """Test de l'initialisation avec configuration par défaut."""
        # Setup mocks
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor
        mock_thread_pool.return_value = Mock()

        mock_config = Mock()
        mock_config.chunk_size = 1500
        mock_config.overlap_size = 300
        mock_from_env.return_value = mock_config

        retriever = SyncContentRetriever(self.mock_client)

        self.assertEqual(retriever.config, mock_config)
        mock_from_env.assert_called_once()

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.TextProcessor"
    )
    def test_retrieve_content_success(
        self, mock_text_processor, mock_thread_pool, mock_attachment_processor_class
    ):
        """Test de récupération de contenu réussie."""
        # Setup mocks
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor
        mock_thread_pool.return_value = Mock()
        mock_text_processor.html_to_plain_text.return_value = "Plain text content"

        retriever = SyncContentRetriever(self.mock_client, self.processing_config)

        # Mock du client pour retourner le contenu
        self.mock_client.get_content.return_value = self.test_content_item

        # Mock de la méthode de traitement des pièces jointes
        retriever._process_content_attachments = Mock()

        result = retriever.retrieve_content("123", process_attachments=True)

        # Vérifications
        self.assertEqual(result.id, "123")
        self.assertEqual(result.title, "Test Page")
        self.assertEqual(result.body_plain, "Plain text content")

        # Vérifier les appels
        self.mock_client.get_content.assert_called_once_with("123")
        mock_text_processor.html_to_plain_text.assert_called_once_with(
            self.test_content_item.body_view
        )
        retriever._process_content_attachments.assert_called_once_with(
            self.test_content_item
        )

        # Vérifier les statistiques
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["content_retrieved"], 1)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager"
    )
    def test_retrieve_content_without_attachments(
        self, mock_thread_pool, mock_attachment_processor_class
    ):
        """Test de récupération de contenu sans traitement des pièces jointes."""
        # Setup mocks
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor
        mock_thread_pool.return_value = Mock()

        retriever = SyncContentRetriever(self.mock_client, self.processing_config)

        # Mock du client
        self.mock_client.get_content.return_value = self.test_content_item

        # Mock de la méthode de traitement des pièces jointes
        retriever._process_content_attachments = Mock()

        result = retriever.retrieve_content("123", process_attachments=False)

        # Vérifications
        self.assertEqual(result.id, "123")
        self.assertEqual(result.title, "Test Page")

        # Vérifier que le traitement des pièces jointes n'a pas été appelé
        retriever._process_content_attachments.assert_not_called()

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager"
    )
    def test_retrieve_content_error(
        self, mock_thread_pool, mock_attachment_processor_class
    ):
        """Test de gestion d'erreur lors de la récupération."""
        # Setup mocks
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor
        mock_thread_pool.return_value = Mock()

        retriever = SyncContentRetriever(self.mock_client, self.processing_config)

        # Mock du client pour lever une exception
        self.mock_client.get_content.side_effect = Exception("API Error")

        with self.assertRaises(ContentProcessingError) as context:
            retriever.retrieve_content("123")

        self.assertIn("Content retrieval failed for 123", str(context.exception))

        # Vérifier les statistiques d'erreur
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["errors"], 1)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.ThreadPoolExecutor"
    )
    def test_process_content_attachments(
        self, mock_executor_class, mock_thread_pool, mock_attachment_processor_class
    ):
        """Test de traitement des pièces jointes."""
        # Setup mocks
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor
        mock_thread_pool.return_value = Mock()

        # Mock ThreadPoolExecutor
        mock_executor = Mock()
        mock_executor_class.return_value.__enter__.return_value = mock_executor

        # Mock futures
        mock_future = Mock()
        mock_future.result.return_value = self.test_attachment
        mock_executor.submit.return_value = mock_future

        # Mock as_completed
        with patch(
            "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.as_completed"
        ) as mock_as_completed:
            mock_as_completed.return_value = [mock_future]

            retriever = SyncContentRetriever(self.mock_client, self.processing_config)

            # Mock du client pour retourner des pièces jointes
            self.mock_client.get_attachments.return_value = [self.test_attachment]

            # Mock du processeur de pièces jointes
            mock_attachment_processor.process_attachment.return_value = (
                self.test_attachment
            )

            # Appeler la méthode
            content_item = self.test_content_item.model_copy()
            retriever._process_content_attachments(content_item)

            # Vérifications
            self.mock_client.get_attachments.assert_called_once_with("123")
            mock_executor.submit.assert_called_once()
            self.assertEqual(len(content_item.attachments), 1)
            self.assertEqual(content_item.attachments[0].id, "att123")

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.ThreadPoolExecutor"
    )
    def test_search_and_retrieve(
        self, mock_executor_class, mock_thread_pool, mock_attachment_processor_class
    ):
        """Test de recherche et récupération."""
        # Setup mocks
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor
        mock_thread_pool.return_value = Mock()

        # Mock ThreadPoolExecutor
        mock_executor = Mock()
        mock_executor_class.return_value.__enter__.return_value = mock_executor

        # Mock futures
        mock_future = Mock()
        mock_future.result.return_value = self.test_content_item
        mock_executor.submit.return_value = mock_future

        # Mock as_completed
        with patch(
            "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.as_completed"
        ) as mock_as_completed:
            mock_as_completed.return_value = [mock_future]

            retriever = SyncContentRetriever(self.mock_client, self.processing_config)

            # Mock du client pour la recherche
            self.mock_client.search_content.return_value = [self.test_content_item]

            # Mock de la méthode de traitement
            retriever._process_single_content = Mock(
                return_value=self.test_content_item
            )

            criteria = SearchCriteria(spaces=["TEST"], max_results=10)
            result = retriever.search_and_retrieve(criteria, process_attachments=True)

            # Vérifications
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0].id, "123")
            self.mock_client.search_content.assert_called_once_with(criteria)

    def test_get_and_reset_stats(self):
        """Test des statistiques."""
        with patch(
            "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor"
        ), patch(
            "src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager"
        ):
            retriever = SyncContentRetriever(self.mock_client, self.processing_config)

            # Modifier les stats
            retriever._stats["content_retrieved"] = 5
            retriever._stats["errors"] = 2

            # Mock des stats du processeur de pièces jointes
            retriever.attachment_processor.get_processing_stats.return_value = {
                "processed": 3
            }

            stats = retriever.get_retrieval_stats()
            self.assertEqual(stats["content_retrieved"], 5)
            self.assertEqual(stats["errors"], 2)
            self.assertEqual(stats["attachment_processed"], 3)

            # Reset
            retriever.reset_stats()
            stats = retriever.get_retrieval_stats()
            self.assertEqual(stats["content_retrieved"], 0)
            self.assertEqual(stats["errors"], 0)


if __name__ == "__main__":
    unittest.main()
