#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour vérifier que ConfluenceLoader désactive le change tracking.
"""

import os
import unittest
from unittest.mock import patch

from ..config import ProcessingConfig


class TestConfluenceLoaderChangeTracking(unittest.TestCase):
    """Tests pour le change tracking dans ConfluenceLoader."""

    def test_processing_config_change_tracking_default(self):
        """Test que ProcessingConfig a le change tracking activé par défaut."""
        config = ProcessingConfig()
        self.assertTrue(config.enable_change_tracking)

    def test_processing_config_from_env_default(self):
        """Test que ProcessingConfig.from_env() active le change tracking par défaut."""
        config = ProcessingConfig.from_env()
        self.assertTrue(config.enable_change_tracking)

    def test_processing_config_from_env_disabled(self):
        """Test que ProcessingConfig.from_env() peut désactiver le change tracking."""
        with patch.dict(os.environ, {"ENABLE_CHANGE_TRACKING": "false"}):
            config = ProcessingConfig.from_env()
            self.assertFalse(config.enable_change_tracking)

    def test_processing_config_from_env_enabled_explicitly(self):
        """Test que ProcessingConfig.from_env() peut activer le change tracking explicitement."""
        with patch.dict(os.environ, {"ENABLE_CHANGE_TRACKING": "true"}):
            config = ProcessingConfig.from_env()
            self.assertTrue(config.enable_change_tracking)

    def test_processing_config_from_env_various_values(self):
        """Test différentes valeurs pour ENABLE_CHANGE_TRACKING."""
        test_cases = [
            ("true", True),
            ("True", True),
            ("TRUE", True),
            ("1", True),
            ("yes", True),
            ("false", False),
            ("False", False),
            ("FALSE", False),
            ("0", False),
            ("no", False),
            ("invalid", False),  # Valeur invalide = False
            ("", False),  # Valeur vide = False
        ]

        for env_value, expected in test_cases:
            with self.subTest(env_value=env_value):
                with patch.dict(os.environ, {"ENABLE_CHANGE_TRACKING": env_value}):
                    config = ProcessingConfig.from_env()
                    self.assertEqual(config.enable_change_tracking, expected)

    def test_confluence_loader_disables_change_tracking_in_code(self):
        """Test que le code du ConfluenceLoader désactive bien le change tracking."""
        # Simuler le comportement du ConfluenceLoader
        from ..config import ProcessingConfig

        # Créer une configuration comme le fait ConfluenceLoader
        processing_config = ProcessingConfig.from_env()
        # Le ConfluenceLoader désactive explicitement le change tracking
        processing_config.enable_change_tracking = False

        # Vérifier que le change tracking est désactivé
        self.assertFalse(processing_config.enable_change_tracking)

    def test_confluence_loader_overrides_env_variable_in_code(self):
        """Test que le ConfluenceLoader désactive le change tracking même si la variable d'environnement l'active."""
        with patch.dict(os.environ, {"ENABLE_CHANGE_TRACKING": "true"}):
            from ..config import ProcessingConfig

            # Créer une configuration comme le fait ConfluenceLoader
            processing_config = ProcessingConfig.from_env()
            # Vérifier que la variable d'environnement l'active
            self.assertTrue(processing_config.enable_change_tracking)

            # Le ConfluenceLoader désactive explicitement le change tracking
            processing_config.enable_change_tracking = False

            # Vérifier que le change tracking est désactivé malgré la variable d'environnement
            self.assertFalse(processing_config.enable_change_tracking)


if __name__ == "__main__":
    unittest.main()
