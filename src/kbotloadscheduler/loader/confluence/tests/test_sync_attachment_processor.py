#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le processeur de pièces jointes synchrone.
"""

import unittest
from datetime import datetime
from unittest.mock import Mock, patch

from pydantic import SecretStr

from ..config import ConfluenceConfig, ProcessingConfig
from ..exceptions import AttachmentProcessingError
from ..models import AttachmentDetail, UserInfo
from ..processing.enums import ProcessingStatus
from ..processing.sync_attachment_processor import SyncAttachmentProcessor
from ..sync_client import SyncConfluenceClient


class TestSyncAttachmentProcessor(unittest.TestCase):
    """Tests pour SyncAttachmentProcessor."""

    def setUp(self):
        """Configuration des tests."""
        # Configuration
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net", pat_token=SecretStr("test_token")
        )

        self.processing_config = ProcessingConfig(
            chunk_size=1000,
            overlap_size=200,
            max_parallel_downloads=3,
            max_thread_workers=2,
        )

        # Mock client
        self.mock_client = Mock(spec=SyncConfluenceClient)

        # Données de test
        self.test_user = UserInfo(
            id="user123", username="testuser", display_name="Test User"
        )

        self.test_attachment = AttachmentDetail(
            id="att123",
            title="test.pdf",
            file_name="test.pdf",
            file_size=1024,
            media_type="application/pdf",
            download_url="https://test.atlassian.net/download/test.pdf",
            created=datetime.now(),
            last_updated=datetime.now(),
            creator=self.test_user,
            content_id="123",
        )

        self.test_attachment_txt = AttachmentDetail(
            id="att456",
            title="document.txt",
            file_name="document.txt",
            file_size=512,
            media_type="text/plain",
            download_url="https://test.atlassian.net/download/document.txt",
            created=datetime.now(),
            last_updated=datetime.now(),
            creator=self.test_user,
            content_id="123",
        )

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
    )
    def test_attachment_processor_initialization(
        self, mock_thread_pool, mock_extractor_class
    ):
        """Test de l'initialisation du processeur de pièces jointes."""
        # Setup mocks
        mock_extractor = Mock()
        mock_extractor_class.return_value = mock_extractor
        mock_thread_pool.return_value = Mock()

        processor = SyncAttachmentProcessor(self.mock_client, self.processing_config)

        self.assertEqual(processor.client, self.mock_client)
        self.assertEqual(processor.config, self.processing_config)
        self.assertIsNotNone(processor.extractor)
        self.assertIsNotNone(processor.thread_pool_manager)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.SecurityValidator"
    )
    def test_process_attachment_success(
        self, mock_security, mock_thread_pool, mock_extractor_class
    ):
        """Test de traitement de pièce jointe réussi."""
        # Setup mocks
        mock_extractor = Mock()
        mock_extractor_class.return_value = mock_extractor

        mock_thread_pool_manager = Mock()
        mock_thread_pool.return_value = mock_thread_pool_manager

        # Mock future pour l'extraction
        mock_future = Mock()
        mock_future.result.return_value = {
            "text": "Extracted text content",
            "metadata": {"pages": 1},
        }
        mock_thread_pool_manager.submit_to_pool.return_value = mock_future

        processor = SyncAttachmentProcessor(self.mock_client, self.processing_config)

        # Mock du téléchargement
        self.mock_client.download_attachment.return_value = b"PDF content bytes"

        # Mock de la validation de sécurité
        mock_security.validate_attachment.return_value = None

        result = processor.process_attachment(self.test_attachment)

        # Vérifications
        self.assertEqual(result.processing_status, ProcessingStatus.SUCCESS.value)
        self.assertEqual(result.extracted_text, "Extracted text content")

        # Vérifier les appels
        mock_security.validate_attachment.assert_called_once_with(
            "test.pdf", "application/pdf"
        )
        self.mock_client.download_attachment.assert_called_once_with(
            self.test_attachment
        )
        mock_thread_pool_manager.submit_to_pool.assert_called_once_with(
            "document",
            mock_extractor.extract_text,
            b"PDF content bytes",
            "test.pdf",
            "application/pdf",
        )

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 1)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.SecurityValidator"
    )
    def test_process_attachment_no_text_extracted(
        self, mock_security, mock_thread_pool, mock_extractor_class
    ):
        """Test de traitement sans extraction de texte."""
        # Setup mocks
        mock_extractor = Mock()
        mock_extractor_class.return_value = mock_extractor

        mock_thread_pool_manager = Mock()
        mock_thread_pool.return_value = mock_thread_pool_manager

        # Mock future pour l'extraction (pas de texte)
        mock_future = Mock()
        mock_future.result.return_value = {"text": "", "metadata": {}}
        mock_thread_pool_manager.submit_to_pool.return_value = mock_future

        processor = SyncAttachmentProcessor(self.mock_client, self.processing_config)

        # Mock du téléchargement
        self.mock_client.download_attachment.return_value = b"Binary content"

        # Mock de la validation de sécurité
        mock_security.validate_attachment.return_value = None

        result = processor.process_attachment(self.test_attachment)

        # Vérifications
        self.assertEqual(result.processing_status, ProcessingStatus.NO_TEXT.value)
        self.assertEqual(result.extracted_text, "[No text extracted: test.pdf]")

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 1)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.SecurityValidator"
    )
    def test_process_attachment_security_error(
        self, mock_security, mock_thread_pool, mock_extractor_class
    ):
        """Test de gestion d'erreur de sécurité."""
        # Setup mocks
        mock_extractor = Mock()
        mock_extractor_class.return_value = mock_extractor
        mock_thread_pool.return_value = Mock()

        processor = SyncAttachmentProcessor(self.mock_client, self.processing_config)

        # Mock de la validation de sécurité pour lever une exception
        mock_security.validate_attachment.side_effect = Exception(
            "Security validation failed"
        )

        with self.assertRaises(AttachmentProcessingError) as context:
            processor.process_attachment(self.test_attachment)

        self.assertIn("Attachment processing failed", str(context.exception))

        # Vérifier les statistiques d'erreur
        stats = processor.get_processing_stats()
        self.assertEqual(stats["failed"], 1)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.SecurityValidator"
    )
    def test_process_attachment_download_error(
        self, mock_security, mock_thread_pool, mock_extractor_class
    ):
        """Test de gestion d'erreur de téléchargement."""
        # Setup mocks
        mock_extractor = Mock()
        mock_extractor_class.return_value = mock_extractor
        mock_thread_pool.return_value = Mock()

        processor = SyncAttachmentProcessor(self.mock_client, self.processing_config)

        # Mock de la validation de sécurité
        mock_security.validate_attachment.return_value = None

        # Mock du téléchargement pour lever une exception
        self.mock_client.download_attachment.side_effect = Exception("Download failed")

        with self.assertRaises(AttachmentProcessingError):
            processor.process_attachment(self.test_attachment)

        # Vérifier les statistiques d'erreur
        stats = processor.get_processing_stats()
        self.assertEqual(stats["failed"], 1)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
    )
    def test_should_process_attachment_with_storage_config(
        self, mock_thread_pool, mock_extractor_class
    ):
        """Test de la logique de décision de traitement avec configuration de stockage."""
        # Setup mocks
        mock_extractor = Mock()
        mock_extractor_class.return_value = mock_extractor
        mock_thread_pool.return_value = Mock()

        # Configuration de stockage avec extensions spécifiques
        storage_config = Mock()
        storage_config.attachment_extensions_to_convert = [".pdf", ".docx"]
        storage_config.attachment_extensions_to_download_raw = [".png", ".jpg"]

        processor = SyncAttachmentProcessor(
            self.mock_client, self.processing_config, storage_config
        )

        # Test avec PDF (dans la liste de conversion)
        should_process = processor._should_process_attachment(self.test_attachment)
        self.assertTrue(should_process)

        # Test avec TXT (pas dans les listes)
        should_process = processor._should_process_attachment(self.test_attachment_txt)
        self.assertFalse(should_process)

        # Test avec PNG (dans la liste de téléchargement brut)
        png_attachment = AttachmentDetail(
            id="att789",
            title="image.png",
            file_name="image.png",
            file_size=2048,
            media_type="image/png",
            download_url="https://test.atlassian.net/download/image.png",
            created=datetime.now(),
            last_updated=datetime.now(),
            creator=self.test_user,
            content_id="123",
        )
        should_process = processor._should_process_attachment(png_attachment)
        self.assertTrue(should_process)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
    )
    def test_should_process_attachment_without_storage_config(
        self, mock_thread_pool, mock_extractor_class
    ):
        """Test de la logique de décision sans configuration de stockage."""
        # Setup mocks
        mock_extractor = Mock()
        mock_extractor_class.return_value = mock_extractor
        mock_thread_pool.return_value = Mock()

        processor = SyncAttachmentProcessor(self.mock_client, self.processing_config)

        # Sans configuration de stockage, tous les fichiers devraient être traités
        should_process = processor._should_process_attachment(self.test_attachment)
        self.assertTrue(should_process)

        should_process = processor._should_process_attachment(self.test_attachment_txt)
        self.assertTrue(should_process)

    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
    )
    @patch(
        "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.SecurityValidator"
    )
    def test_process_attachment_skipped(
        self, mock_security, mock_thread_pool, mock_extractor_class
    ):
        """Test de traitement d'une pièce jointe ignorée."""
        # Setup mocks
        mock_extractor = Mock()
        mock_extractor_class.return_value = mock_extractor
        mock_thread_pool.return_value = Mock()

        # Configuration de stockage qui n'inclut pas les fichiers TXT
        storage_config = Mock()
        storage_config.attachment_extensions_to_convert = [".pdf"]
        storage_config.attachment_extensions_to_download_raw = [".png"]

        processor = SyncAttachmentProcessor(
            self.mock_client, self.processing_config, storage_config
        )

        # Mock de la validation de sécurité
        mock_security.validate_attachment.return_value = None

        result = processor.process_attachment(self.test_attachment_txt)

        # Vérifications
        self.assertEqual(result.processing_status, ProcessingStatus.SKIPPED.value)
        self.assertEqual(result.extracted_text, "[Skipped: document.txt]")

        # Vérifier que le téléchargement n'a pas été appelé
        self.mock_client.download_attachment.assert_not_called()

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["skipped"], 1)

    def test_get_and_reset_stats(self):
        """Test des statistiques."""
        with patch(
            "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.DocumentExtractor"
        ), patch(
            "src.kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager"
        ):
            processor = SyncAttachmentProcessor(
                self.mock_client, self.processing_config
            )

            # Modifier les stats
            processor._stats["processed"] = 5
            processor._stats["failed"] = 2
            processor._stats["skipped"] = 1

            stats = processor.get_processing_stats()
            self.assertEqual(stats["processed"], 5)
            self.assertEqual(stats["failed"], 2)
            self.assertEqual(stats["skipped"], 1)

            # Reset
            processor.reset_stats()
            stats = processor.get_processing_stats()
            self.assertEqual(stats["processed"], 0)
            self.assertEqual(stats["failed"], 0)
            self.assertEqual(stats["skipped"], 0)


if __name__ == "__main__":
    unittest.main()
