#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le module de configuration.
"""

import os
import unittest
from unittest.mock import patch

from pydantic import ValidationError, SecretStr

from ..config import (
    ConfluenceConfig,
    SearchCriteria,
    StorageConfig,
    ThreadPoolConfig,
    RetryConfig,
    CircuitBreakerConfig,
    Environment,
    EnvironmentConfig,
)


class TestConfluenceConfig(unittest.TestCase):
    """Tests pour la classe ConfluenceConfig."""

    def test_confluence_config_creation_with_pat(self):
        """Test de création d'une configuration avec PAT token."""
        config = ConfluenceConfig(
            url="https://test.atlassian.net", pat_token=SecretStr("test_pat_token")
        )

        self.assertEqual(str(config.url), "https://test.atlassian.net/")
        self.assertEqual(config.pat_token.get_secret_value(), "test_pat_token")
        self.assertIsNone(config.username)
        self.assertIsNone(config.api_token)
        self.assertEqual(config.default_space_key, "EXAMPLE")
        self.assertEqual(config.timeout, 30)

    def test_confluence_config_creation_with_api_token(self):
        """Test de création d'une configuration avec API token."""
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="test_user",
            pat_token=SecretStr("test_api_token"),
        )

        self.assertEqual(str(config.url), "https://test.atlassian.net/")
        self.assertEqual(config.username, "test_user")
        self.assertEqual(config.api_token.get_secret_value(), "test_api_token")
        self.assertIsNone(config.pat_token)

    def test_confluence_config_parallel_pagination_defaults(self):
        """Test des valeurs par défaut pour la pagination parallèle."""
        config = ConfluenceConfig(
            url="https://test.atlassian.net", pat_token=SecretStr("test_token")
        )

        self.assertTrue(config.enable_parallel_pagination)
        self.assertEqual(config.max_parallel_requests, 3)
        self.assertEqual(config.parallel_pagination_threshold, 200)

    @patch.dict(
        os.environ,
        {
            "CONFLUENCE_URL": "https://env.atlassian.net",
            "CONFLUENCE_PAT_TOKEN": "env_pat_token",
            "CONFLUENCE_TIMEOUT": "60",
        },
    )
    def test_confluence_config_from_env(self):
        """Test de création d'une configuration depuis les variables d'environnement."""
        config = ConfluenceConfig.from_env()

        self.assertEqual(str(config.url), "https://env.atlassian.net/")
        self.assertEqual(config.pat_token.get_secret_value(), "env_pat_token")
        self.assertEqual(config.timeout, 60)

    def test_confluence_config_invalid_url(self):
        """Test d'erreur avec URL invalide."""
        with self.assertRaises(ValidationError):
            ConfluenceConfig(url="invalid-url", pat_token=SecretStr("test_token"))


class TestSearchCriteria(unittest.TestCase):
    """Tests pour la classe SearchCriteria."""

    def test_search_criteria_creation(self):
        """Test de création de critères de recherche."""
        criteria = SearchCriteria(
            spaces=["SPACE1", "SPACE2"],
            content_types=["page", "blogpost"],
            labels=["important", "draft"],
            max_results=500,
            include_attachments=True,
            last_modified_days=30,
        )

        self.assertEqual(criteria.spaces, ["SPACE1", "SPACE2"])
        self.assertEqual(criteria.content_types, ["page", "blogpost"])
        self.assertEqual(criteria.labels, ["important", "draft"])
        self.assertEqual(criteria.max_results, 500)
        self.assertTrue(criteria.include_attachments)
        self.assertEqual(criteria.last_modified_days, 30)

    def test_search_criteria_defaults(self):
        """Test des valeurs par défaut des critères de recherche."""
        criteria = SearchCriteria()

        self.assertEqual(criteria.spaces, [])
        self.assertEqual(criteria.content_types, ["page"])
        self.assertEqual(criteria.labels, [])
        self.assertEqual(criteria.max_results, 1000)
        self.assertTrue(criteria.include_attachments)
        self.assertIsNone(criteria.last_modified_days)

    def test_search_criteria_validation_max_results(self):
        """Test de validation du nombre maximum de résultats."""
        with self.assertRaises(ValidationError):
            SearchCriteria(max_results=0)

        with self.assertRaises(ValidationError):
            SearchCriteria(max_results=10001)

    def test_search_criteria_validation_last_modified_days(self):
        """Test de validation des jours de modification."""
        with self.assertRaises(ValidationError):
            SearchCriteria(last_modified_days=0)


class TestStorageConfig(unittest.TestCase):
    """Tests pour la classe StorageConfig."""

    def test_storage_config_filesystem(self):
        """Test de configuration de stockage filesystem."""
        config = StorageConfig(type="filesystem", base_dir="/custom/path")

        self.assertEqual(config.type, "filesystem")
        self.assertEqual(config.base_dir, "/custom/path")
        self.assertIsNone(config.bucket_name)

    def test_storage_config_gcs(self):
        """Test de configuration de stockage GCS."""
        config = StorageConfig(
            type="gcs", bucket_name="my-bucket", base_prefix="confluence-data"
        )

        self.assertEqual(config.type, "gcs")
        self.assertEqual(config.bucket_name, "my-bucket")
        self.assertEqual(config.base_prefix, "confluence-data")

    def test_storage_config_defaults(self):
        """Test des valeurs par défaut de StorageConfig."""
        config = StorageConfig()

        self.assertEqual(config.type, "filesystem")
        self.assertEqual(config.base_dir, "output_data_dir")
        self.assertIsNone(config.bucket_name)
        self.assertEqual(config.base_prefix, "confluence_rag")


class TestThreadPoolConfig(unittest.TestCase):
    """Tests pour la classe ThreadPoolConfig."""

    def test_thread_pool_config_creation(self):
        """Test de création d'une configuration de pool de threads."""
        config = ThreadPoolConfig(
            max_workers=10, thread_name_prefix="custom-worker", enable_monitoring=True
        )

        self.assertEqual(config.max_workers, 10)
        self.assertEqual(config.thread_name_prefix, "custom-worker")
        self.assertTrue(config.enable_monitoring)

    def test_thread_pool_config_defaults(self):
        """Test des valeurs par défaut de ThreadPoolConfig."""
        config = ThreadPoolConfig()

        self.assertEqual(config.max_workers, 4)
        self.assertEqual(config.thread_name_prefix, "ConfluenceRAG-Worker")
        self.assertFalse(config.enable_monitoring)

    def test_thread_pool_config_validation(self):
        """Test de validation de ThreadPoolConfig."""
        with self.assertRaises(ValidationError):
            ThreadPoolConfig(max_workers=0)


class TestRetryConfig(unittest.TestCase):
    """Tests pour la classe RetryConfig."""

    def test_retry_config_creation(self):
        """Test de création d'une configuration de retry."""
        config = RetryConfig(
            max_attempts=5, base_delay=2.0, max_delay=60.0, exponential_base=3.0
        )

        self.assertEqual(config.max_attempts, 5)
        self.assertEqual(config.base_delay, 2.0)
        self.assertEqual(config.max_delay, 60.0)
        self.assertEqual(config.exponential_base, 3.0)

    def test_retry_config_defaults(self):
        """Test des valeurs par défaut de RetryConfig."""
        config = RetryConfig()

        self.assertEqual(config.max_attempts, 3)
        self.assertEqual(config.base_delay, 1.0)
        self.assertEqual(config.max_delay, 30.0)
        self.assertEqual(config.exponential_base, 2.0)

    def test_retry_config_validation(self):
        """Test de validation de RetryConfig."""
        with self.assertRaises(ValidationError):
            RetryConfig(max_attempts=0)

        with self.assertRaises(ValidationError):
            RetryConfig(base_delay=-1.0)


class TestCircuitBreakerConfig(unittest.TestCase):
    """Tests pour la classe CircuitBreakerConfig."""

    def test_circuit_breaker_config_creation(self):
        """Test de création d'une configuration de circuit breaker."""
        config = CircuitBreakerConfig(
            failure_threshold=10, recovery_timeout=120, expected_exception=Exception
        )

        self.assertEqual(config.failure_threshold, 10)
        self.assertEqual(config.recovery_timeout, 120)
        self.assertEqual(config.expected_exception, Exception)

    def test_circuit_breaker_config_defaults(self):
        """Test des valeurs par défaut de CircuitBreakerConfig."""
        config = CircuitBreakerConfig()

        self.assertEqual(config.failure_threshold, 5)
        self.assertEqual(config.recovery_timeout, 60)
        self.assertEqual(config.expected_exception, Exception)


class TestEnvironmentConfig(unittest.TestCase):
    """Tests pour la classe EnvironmentConfig."""

    def test_environment_config_creation(self):
        """Test de création d'une configuration d'environnement."""
        config = EnvironmentConfig(
            active_environment=Environment.PROD, config_file_path="/path/to/config.json"
        )

        self.assertEqual(config.active_environment, Environment.PROD)
        self.assertEqual(config.config_file_path, "/path/to/config.json")

    @patch.dict(
        os.environ,
        {
            "CONFLUENCE_RAG_ENVIRONMENT": "prod",
            "CONFLUENCE_RAG_CONFIG_FILE": "/custom/config.json",
        },
    )
    def test_environment_config_from_env(self):
        """Test de création depuis les variables d'environnement."""
        config = EnvironmentConfig.from_env()

        self.assertEqual(config.active_environment, Environment.PROD)
        self.assertEqual(config.config_file_path, "/custom/config.json")

    @patch.dict(os.environ, {"CONFLUENCE_RAG_ENVIRONMENT": "invalid"})
    def test_environment_config_invalid_env(self):
        """Test avec environnement invalide."""
        with patch(
            "kbotloadscheduler.loader.confluence.config.logging.warning"
        ) as mock_warning:
            config = EnvironmentConfig.from_env()

            self.assertEqual(config.active_environment, Environment.DEV)
            mock_warning.assert_called_once()


if __name__ == "__main__":
    unittest.main()
