#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour exécuter tous les tests de l'orchestrateur synchrone.
"""

import os
import sys
import time
import unittest
from io import StringIO
# Import des modules de test
from test_sync_client import TestSyncConfluenceClient
from test_sync_orchestrator import TestSyncOrchestrator
from test_sync_content_retriever import TestSyncContentRetriever
from test_sync_attachment_processor import TestSyncAttachmentProcessor
from test_sync_integration import TestSyncIntegration
from test_sync_performance import TestSyncPerformance
# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))


class SyncTestRunner:
    """Runner personnalisé pour les tests synchrones."""

    def __init__(self):
        self.results = {}
        self.total_tests = 0
        self.total_failures = 0
        self.total_errors = 0
        self.total_time = 0

    def run_test_suite(self, test_class, suite_name: str, verbose: bool = True):
        """Exécute une suite de tests et collecte les résultats."""
        print(f"\n{'=' * 60}")
        print(f"Exécution de {suite_name}")
        print(f"{'=' * 60}")

        # Créer la suite de tests
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromTestCase(test_class)

        # Capturer la sortie
        stream = StringIO()
        runner = unittest.TextTestRunner(stream=stream, verbosity=2 if verbose else 1)

        # Mesurer le temps d'exécution
        start_time = time.time()
        result = runner.run(suite)
        execution_time = time.time() - start_time

        # Collecter les résultats
        self.results[suite_name] = {
            "tests_run": result.testsRun,
            "failures": len(result.failures),
            "errors": len(result.errors),
            "execution_time": execution_time,
            "success_rate": (
                (result.testsRun - len(result.failures) - len(result.errors))
                / result.testsRun
                * 100
                if result.testsRun > 0
                else 0
            ),
        }

        # Mettre à jour les totaux
        self.total_tests += result.testsRun
        self.total_failures += len(result.failures)
        self.total_errors += len(result.errors)
        self.total_time += execution_time

        # Afficher les résultats
        print(f"Tests exécutés: {result.testsRun}")
        print(f"Échecs: {len(result.failures)}")
        print(f"Erreurs: {len(result.errors)}")
        print(f"Temps d'exécution: {execution_time:.2f}s")
        print(f"Taux de réussite: {self.results[suite_name]['success_rate']:.1f}%")

        # Afficher les détails des échecs et erreurs si verbose
        if verbose and (result.failures or result.errors):
            print("\nDétails des échecs et erreurs:")
            output = stream.getvalue()
            print(output)

        return result.wasSuccessful()

    def run_all_tests(self, include_performance: bool = False, verbose: bool = True):
        """Exécute tous les tests synchrones."""
        print("🚀 Démarrage des tests de l'orchestrateur Confluence synchrone")
        print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Suites de tests à exécuter
        test_suites = [
            (TestSyncConfluenceClient, "Client Synchrone"),
            (TestSyncOrchestrator, "Orchestrateur Synchrone"),
            (TestSyncContentRetriever, "Récupérateur de Contenu Synchrone"),
            (TestSyncAttachmentProcessor, "Processeur de Pièces Jointes Synchrone"),
            (TestSyncIntegration, "Tests d'Intégration Synchrone"),
        ]

        # Ajouter les tests de performance si demandé
        if include_performance:
            test_suites.append((TestSyncPerformance, "Tests de Performance"))

        # Exécuter chaque suite
        all_successful = True
        for test_class, suite_name in test_suites:
            try:
                success = self.run_test_suite(test_class, suite_name, verbose)
                if not success:
                    all_successful = False
            except Exception as e:
                print(f"❌ Erreur lors de l'exécution de {suite_name}: {e}")
                all_successful = False

        # Afficher le résumé final
        self.print_summary()

        return all_successful

    def print_summary(self):
        """Affiche un résumé des résultats de tous les tests."""
        print(f"\n{'=' * 80}")
        print("📊 RÉSUMÉ DES TESTS SYNCHRONES")
        print(f"{'=' * 80}")

        # Résumé global
        print(f"Total des tests: {self.total_tests}")
        print(f"Échecs: {self.total_failures}")
        print(f"Erreurs: {self.total_errors}")
        print(f"Temps total: {self.total_time:.2f}s")

        overall_success_rate = (
            (self.total_tests - self.total_failures - self.total_errors)
            / self.total_tests
            * 100
            if self.total_tests > 0
            else 0
        )
        print(f"Taux de réussite global: {overall_success_rate:.1f}%")

        # Détails par suite
        print(
            f"\n{'Suite de tests':<40} {'Tests':<8} {'Échecs':<8} {'Erreurs':<8} {'Temps':<10} {'Réussite':<10}"
        )
        print("-" * 80)

        for suite_name, results in self.results.items():
            print(
                f"{suite_name:<40} {results['tests_run']:<8} {results['failures']:<8} "
                f"{results['errors']:<8} {results['execution_time']:<10.2f} "
                f"{results['success_rate']:<10.1f}%")

        # Statut final
        if self.total_failures == 0 and self.total_errors == 0:
            print("\n🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!")
            print("✅ L'orchestrateur synchrone est prêt pour la production")
        else:
            print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
            print(f"❌ {self.total_failures} échecs, {self.total_errors} erreurs")

        # Recommandations
        print("\n📋 RECOMMANDATIONS:")
        if overall_success_rate >= 95:
            print("✅ Excellent taux de réussite - Prêt pour l'intégration")
        elif overall_success_rate >= 85:
            print("⚠️  Bon taux de réussite - Quelques ajustements recommandés")
        else:
            print("❌ Taux de réussite insuffisant - Corrections nécessaires")


def main():
    """Point d'entrée principal."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Exécute les tests de l'orchestrateur synchrone"
    )
    parser.add_argument(
        "--performance", action="store_true", help="Inclure les tests de performance"
    )
    parser.add_argument(
        "--quiet", action="store_true", help="Mode silencieux (moins de verbosité)"
    )
    parser.add_argument(
        "--suite",
        choices=[
            "client",
            "orchestrator",
            "retriever",
            "processor",
            "integration",
            "performance",
        ],
        help="Exécuter une suite spécifique",
    )

    args = parser.parse_args()

    runner = SyncTestRunner()

    if args.suite:
        # Exécuter une suite spécifique
        suite_map = {
            "client": (TestSyncConfluenceClient, "Client Synchrone"),
            "orchestrator": (TestSyncOrchestrator, "Orchestrateur Synchrone"),
            "retriever": (
                TestSyncContentRetriever,
                "Récupérateur de Contenu Synchrone",
            ),
            "processor": (
                TestSyncAttachmentProcessor,
                "Processeur de Pièces Jointes Synchrone",
            ),
            "integration": (TestSyncIntegration, "Tests d'Intégration Synchrone"),
            "performance": (TestSyncPerformance, "Tests de Performance"),
        }

        test_class, suite_name = suite_map[args.suite]
        success = runner.run_test_suite(test_class, suite_name, not args.quiet)
        runner.print_summary()
    else:
        # Exécuter tous les tests
        success = runner.run_all_tests(
            include_performance=args.performance, verbose=not args.quiet
        )

    # Code de sortie
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
