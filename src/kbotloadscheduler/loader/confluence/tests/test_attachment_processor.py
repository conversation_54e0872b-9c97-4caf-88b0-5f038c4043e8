#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le processeur de pièces jointes.
"""

import asyncio
import logging
import unittest
from unittest.mock import Mock, AsyncMock, patch

from ..config import ProcessingConfig, StorageConfig
from ..exceptions import AttachmentProcessingError
from ..models import AttachmentDetail, UserInfo
from ..processing.attachment_processor import AttachmentProcessor
from ..processing.enums import ProcessingStatus, ExtractionResult


class TestAttachmentProcessor(unittest.TestCase):
    """Tests pour la classe AttachmentProcessor."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)

        # Mock du client Confluence
        self.mock_client = AsyncMock()

        # Configuration de traitement avec mock pour thread_pool_config
        self.processing_config = Mock()
        self.processing_config.max_parallel_downloads = 2
        self.processing_config.max_file_size = 10 * 1024 * 1024

        # Mock pour thread_pool_config avec valeurs numériques
        thread_pool_config = Mock()
        thread_pool_config.io_thread_workers = 5
        thread_pool_config.document_processing_workers = 3
        thread_pool_config.api_thread_workers = 4
        self.processing_config.thread_pool_config = thread_pool_config

        # Configuration de stockage
        self.storage_config = StorageConfig(
            attachment_extensions_to_download_raw=[".pdf", ".docx"],
            attachment_extensions_to_convert=[".txt", ".html"],
        )

        # Créer un utilisateur de test
        self.test_user = UserInfo(
            id="user123", username="testuser", display_name="Test User"
        )

        # Créer une pièce jointe de test
        self.test_attachment = AttachmentDetail(
            id="att123",
            title="test.pdf",
            file_name="test.pdf",
            file_size=1024,
            media_type="application/pdf",
            download_url="https://example.com/download/test.pdf",
            created="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

    @patch("confluence_rag.processing.attachment_processor.get_thread_pool_manager")
    def test_attachment_processor_initialization(self, mock_get_thread_pool_manager):
        """Test de l'initialisation du processeur de pièces jointes."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        processor = AttachmentProcessor(
            self.mock_client, self.processing_config, self.storage_config
        )

        self.assertEqual(processor.client, self.mock_client)
        self.assertEqual(processor.config, self.processing_config)
        self.assertEqual(processor.storage_config, self.storage_config)
        self.assertIsNotNone(processor.extractor)
        self.assertIsNotNone(processor.thread_pool_manager)
        self.assertIsNotNone(processor.download_semaphore)

        # Vérifier les statistiques initiales
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 0)
        self.assertEqual(stats["failed"], 0)
        self.assertEqual(stats["skipped"], 0)

    @patch("confluence_rag.processing.attachment_processor.get_thread_pool_manager")
    def test_attachment_processor_default_config(self, mock_get_thread_pool_manager):
        """Test de l'initialisation avec configuration par défaut."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        with patch(
            "confluence_rag.processing.attachment_processor.ProcessingConfig.from_env"
        ) as mock_from_env:
            # Créer un mock config avec thread_pool_config
            mock_config = Mock()
            mock_config.max_parallel_downloads = 5
            mock_config.thread_pool_config = Mock()
            mock_from_env.return_value = mock_config

            processor = AttachmentProcessor(self.mock_client)

            self.assertEqual(processor.config, mock_config)
            mock_from_env.assert_called_once()

    async def test_process_attachment_raw_download(self):
        """Test de traitement d'une pièce jointe marquée pour téléchargement brut."""
        processor = AttachmentProcessor(
            self.mock_client, self.processing_config, self.storage_config
        )

        # Pièce jointe PDF (dans la liste des téléchargements bruts)
        pdf_attachment = AttachmentDetail(
            id="att123",
            title="document.pdf",
            file_name="document.pdf",
            file_size=1024,
            media_type="application/pdf",
            download_url="https://example.com/download/document.pdf",
            created="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

        result = await processor.process_attachment(pdf_attachment)

        self.assertEqual(result.processing_status, ProcessingStatus.COMPLETED.value)
        self.assertEqual(result.extracted_text, "[Raw file: document.pdf]")
        self.assertEqual(result.processing_metadata["type"], "raw_download")
        self.assertEqual(result.processing_metadata["extension"], ".pdf")

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["skipped"], 1)

    async def test_process_attachment_unsupported_type(self):
        """Test de traitement d'une pièce jointe de type non supporté."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock de l'extracteur pour retourner False
        processor.extractor.can_process = Mock(return_value=False)

        unsupported_attachment = AttachmentDetail(
            id="att123",
            title="unknown.xyz",
            file_name="unknown.xyz",
            file_size=1024,
            media_type="application/unknown",
            download_url="https://example.com/download/unknown.xyz",
            created="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

        result = await processor.process_attachment(unsupported_attachment)

        self.assertEqual(result.processing_status, ProcessingStatus.COMPLETED.value)
        self.assertEqual(result.extracted_text, "[Unsupported document: unknown.xyz]")

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["skipped"], 1)

    async def test_process_attachment_successful_extraction(self):
        """Test de traitement réussi avec extraction de texte."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock du téléchargement
        test_content = b"Test PDF content"
        self.mock_client.download_attachment.return_value = test_content

        # Mock de l'extracteur
        processor.extractor.can_process = Mock(return_value=True)

        # Mock du thread pool manager
        extraction_result = ExtractionResult(
            text="Extracted text content",
            success=True,
            metadata={"pages": 1, "format": "pdf"},
        )
        processor.thread_pool_manager.run_in_document_pool = AsyncMock(
            return_value=extraction_result
        )

        result = await processor.process_attachment(self.test_attachment)

        self.assertEqual(result.processing_status, ProcessingStatus.COMPLETED.value)
        self.assertEqual(result.extracted_text, "Extracted text content")
        self.assertEqual(result.processing_metadata, {"pages": 1, "format": "pdf"})

        # Vérifier les appels
        self.mock_client.download_attachment.assert_called_once_with(
            self.test_attachment
        )
        processor.thread_pool_manager.run_in_document_pool.assert_called_once()

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 1)

    async def test_process_attachment_extraction_failure(self):
        """Test de traitement avec échec d'extraction."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock du téléchargement
        test_content = b"Test content"
        self.mock_client.download_attachment.return_value = test_content

        # Mock de l'extracteur
        processor.extractor.can_process = Mock(return_value=True)

        # Mock du thread pool manager avec échec
        extraction_result = ExtractionResult(
            text="", success=False, error_message="Extraction failed"
        )
        processor.thread_pool_manager.run_in_document_pool = AsyncMock(
            return_value=extraction_result
        )

        result = await processor.process_attachment(self.test_attachment)

        self.assertEqual(result.processing_status, ProcessingStatus.FAILED.value)
        self.assertEqual(result.extracted_text, "[Processing failed: test.pdf]")
        self.assertEqual(result.processing_error, "Extraction failed")

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["failed"], 1)

    async def test_process_attachment_download_error(self):
        """Test de traitement avec erreur de téléchargement."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock de l'extracteur
        processor.extractor.can_process = Mock(return_value=True)

        # Mock du téléchargement avec erreur
        self.mock_client.download_attachment.side_effect = Exception("Download failed")

        with self.assertRaises(AttachmentProcessingError):
            await processor.process_attachment(self.test_attachment)

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["failed"], 1)

    @patch("confluence_rag.processing.attachment_processor.SecurityValidator")
    async def test_process_attachment_security_validation(
        self, mock_security_validator
    ):
        """Test de validation de sécurité lors du traitement."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock de validation de sécurité avec erreur
        mock_security_validator.validate_attachment.side_effect = Exception(
            "Security validation failed"
        )

        with self.assertRaises(AttachmentProcessingError):
            await processor.process_attachment(self.test_attachment)

        # Vérifier que la validation a été appelée
        mock_security_validator.validate_attachment.assert_called_once_with(
            self.test_attachment.file_name, self.test_attachment.media_type
        )

    async def test_process_attachment_semaphore_limiting(self):
        """Test de limitation des téléchargements simultanés."""
        # Configuration avec limite de 1 téléchargement simultané
        limited_config = ProcessingConfig(max_parallel_downloads=1)
        processor = AttachmentProcessor(self.mock_client, limited_config)

        # Mock de l'extracteur
        processor.extractor.can_process = Mock(return_value=True)

        # Mock du téléchargement avec délai
        async def slow_download(attachment):
            await asyncio.sleep(0.1)
            return b"test content"

        self.mock_client.download_attachment.side_effect = slow_download

        # Mock du thread pool manager
        extraction_result = ExtractionResult("text", True)
        processor.thread_pool_manager.run_in_document_pool = AsyncMock(
            return_value=extraction_result
        )

        # Créer plusieurs pièces jointes
        attachments = [
            AttachmentDetail(
                id=f"att{i}",
                title=f"test{i}.txt",
                file_name=f"test{i}.txt",
                file_size=1024,
                media_type="text/plain",
                download_url=f"https://example.com/test{i}.txt",
                created="2023-01-01T12:00:00",
                creator=self.test_user,
                content_id="content123",
            )
            for i in range(3)
        ]

        # Traiter en parallèle
        start_time = asyncio.get_event_loop().time()
        results = await asyncio.gather(
            *[processor.process_attachment(att) for att in attachments]
        )
        end_time = asyncio.get_event_loop().time()

        # Vérifier que tous ont été traités
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertEqual(result.processing_status, ProcessingStatus.COMPLETED.value)

        # Vérifier que le traitement a pris du temps (sérialisation due au semaphore)
        self.assertGreater(
            end_time - start_time, 0.2
        )  # Au moins 0.2s pour 3 téléchargements

    def test_get_processing_stats(self):
        """Test de récupération des statistiques de traitement."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Modifier les statistiques internes
        processor._stats["processed"] = 5
        processor._stats["failed"] = 2
        processor._stats["skipped"] = 1

        stats = processor.get_processing_stats()

        self.assertEqual(stats["processed"], 5)
        self.assertEqual(stats["failed"], 2)
        self.assertEqual(stats["skipped"], 1)

        # Vérifier que c'est une copie (modification ne doit pas affecter l'original)
        stats["processed"] = 10
        original_stats = processor.get_processing_stats()
        self.assertEqual(original_stats["processed"], 5)

    def test_reset_stats(self):
        """Test de remise à zéro des statistiques."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Modifier les statistiques
        processor._stats["processed"] = 5
        processor._stats["failed"] = 2
        processor._stats["skipped"] = 1

        # Remettre à zéro
        processor.reset_stats()

        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 0)
        self.assertEqual(stats["failed"], 0)
        self.assertEqual(stats["skipped"], 0)

    async def test_process_attachment_with_convert_and_raw_flags(self):
        """Test de traitement avec flags de conversion et téléchargement brut."""
        # Configuration avec overlap entre raw et convert
        overlap_config = StorageConfig(
            attachment_extensions_to_download_raw=[".txt", ".pdf"],
            attachment_extensions_to_convert=[".txt", ".html"],
        )

        processor = AttachmentProcessor(
            self.mock_client, self.processing_config, overlap_config
        )

        # Pièce jointe .txt (dans les deux listes)
        txt_attachment = AttachmentDetail(
            id="att123",
            title="document.txt",
            file_name="document.txt",
            file_size=1024,
            media_type="text/plain",
            download_url="https://example.com/document.txt",
            created="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

        # Mock de l'extracteur et du téléchargement
        processor.extractor.can_process = Mock(return_value=True)
        self.mock_client.download_attachment.return_value = b"test content"

        extraction_result = ExtractionResult("Extracted text", True)
        processor.thread_pool_manager.run_in_document_pool = AsyncMock(
            return_value=extraction_result
        )

        result = await processor.process_attachment(txt_attachment)

        # Doit être traité (convert a priorité sur raw)
        self.assertEqual(result.processing_status, ProcessingStatus.COMPLETED.value)
        self.assertEqual(result.extracted_text, "Extracted text")

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 1)


if __name__ == "__main__":
    unittest.main()
