#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Module de health checks pour le système RAG Confluence.
Fournit des vérifications de santé pour tous les composants critiques du système.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, List, Optional

import psutil

from .circuit_breaker import CircuitBreaker, CircuitState
from .config import HealthCheckConfig, ConfluenceConfig
from .exceptions import CircuitOpenError
from .storage import StorageProvider
from .thread_pool_manager import get_thread_pool_manager


class HealthStatus(Enum):
    """États de santé possibles."""

    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Résultat d'un health check."""

    name: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    duration_ms: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convertit le résultat en dictionnaire."""
        return {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "duration_ms": self.duration_ms,
        }


@dataclass
class SystemHealthReport:
    """Rapport de santé global du système."""

    overall_status: HealthStatus
    checks: List[HealthCheckResult] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        """Convertit le rapport en dictionnaire."""
        return {
            "overall_status": self.overall_status.value,
            "timestamp": self.timestamp.isoformat(),
            "checks": [check.to_dict() for check in self.checks],
            "summary": {
                "total_checks": len(self.checks),
                "healthy": len(
                    [c for c in self.checks if c.status == HealthStatus.HEALTHY]
                ),
                "degraded": len(
                    [c for c in self.checks if c.status == HealthStatus.DEGRADED]
                ),
                "unhealthy": len(
                    [c for c in self.checks if c.status == HealthStatus.UNHEALTHY]
                ),
                "unknown": len(
                    [c for c in self.checks if c.status == HealthStatus.UNKNOWN]
                ),
            },
        }


class HealthChecker:
    """Gestionnaire principal des health checks."""

    def __init__(self, config: HealthCheckConfig):
        """
        Initialise le health checker.

        Args:
            config: Configuration des health checks
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._last_check_time: Optional[datetime] = None
        self._cached_report: Optional[SystemHealthReport] = None

    async def check_system_health(
        self,
        confluence_config: Optional[ConfluenceConfig] = None,
        storage_provider: Optional[StorageProvider] = None,
        circuit_breaker: Optional[CircuitBreaker] = None,
    ) -> SystemHealthReport:
        """
        Effectue tous les health checks et retourne un rapport global.

        Args:
            confluence_config: Configuration Confluence pour les tests de connectivité
            storage_provider: Fournisseur de stockage pour les tests
            circuit_breaker: Circuit breaker pour les tests d'état

        Returns:
            Rapport de santé global du système
        """
        if not self.config.enabled:
            return SystemHealthReport(
                overall_status=HealthStatus.UNKNOWN,
                checks=[
                    HealthCheckResult(
                        name="health_checks",
                        status=HealthStatus.UNKNOWN,
                        message="Health checks désactivés",
                    )
                ],
            )

        start_time = time.time()
        checks = []

        # Health check des ressources système
        if self.config.check_system_resources:
            checks.append(await self._check_system_resources())

        # Health check des thread pools
        if self.config.check_thread_pools:
            checks.append(await self._check_thread_pools())

        # Health check de l'API Confluence
        if self.config.check_confluence_api and confluence_config:
            checks.append(await self._check_confluence_api(confluence_config))

        # Health check du stockage
        if self.config.check_storage and storage_provider:
            checks.append(await self._check_storage(storage_provider))

        # Health check des circuit breakers
        if self.config.check_circuit_breakers and circuit_breaker:
            checks.append(await self._check_circuit_breakers(circuit_breaker))

        # Déterminer le statut global
        overall_status = self._determine_overall_status(checks)

        report = SystemHealthReport(overall_status=overall_status, checks=checks)

        # Mettre en cache le rapport
        self._last_check_time = datetime.now()
        self._cached_report = report

        total_time = (time.time() - start_time) * 1000
        self.logger.info(
            f"Health check complet effectué en {total_time:.2f}ms - Statut: {overall_status.value}"
        )

        return report

    async def _check_system_resources(self) -> HealthCheckResult:
        """Vérifie les ressources système (mémoire, disque)."""
        start_time = time.time()

        try:
            # Vérifier la mémoire
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Vérifier l'espace disque
            disk = psutil.disk_usage("/")
            disk_percent = (disk.used / disk.total) * 100

            details = {
                "memory": {
                    "used_percent": memory_percent,
                    "available_gb": round(memory.available / (1024**3), 2),
                    "total_gb": round(memory.total / (1024**3), 2),
                },
                "disk": {
                    "used_percent": round(disk_percent, 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "total_gb": round(disk.total / (1024**3), 2),
                },
            }

            # Déterminer le statut
            if (
                memory_percent > self.config.memory_threshold_percent
                or disk_percent > self.config.disk_threshold_percent
            ):
                status = HealthStatus.UNHEALTHY
                message = f"Ressources critiques: Mémoire {memory_percent:.1f}%, Disque {disk_percent:.1f}%"
            elif memory_percent > (
                self.config.memory_threshold_percent * 0.8
            ) or disk_percent > (self.config.disk_threshold_percent * 0.8):
                status = HealthStatus.DEGRADED
                message = f"Ressources élevées: Mémoire {memory_percent:.1f}%, Disque {disk_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Ressources normales: Mémoire {memory_percent:.1f}%, Disque {disk_percent:.1f}%"

            return HealthCheckResult(
                name="system_resources",
                status=status,
                message=message,
                details=details,
                duration_ms=(time.time() - start_time) * 1000,
            )

        except Exception as e:
            return HealthCheckResult(
                name="system_resources",
                status=HealthStatus.UNKNOWN,
                message=f"Erreur lors de la vérification des ressources: {str(e)}",
                duration_ms=(time.time() - start_time) * 1000,
            )

    async def _check_thread_pools(self) -> HealthCheckResult:
        """Vérifie l'état des thread pools."""
        start_time = time.time()

        try:
            thread_manager = get_thread_pool_manager()
            stats = thread_manager.get_pool_stats()

            details = {
                "pools": stats,
                "active_threads": sum(
                    pool_stats.get("active_threads", 0) for pool_stats in stats.values()
                ),
                "total_workers": sum(
                    pool_stats.get("max_workers", 0) for pool_stats in stats.values()
                ),
            }

            # Vérifier s'il y a des pools surchargés
            overloaded_pools = []
            for pool_name, pool_stats in stats.items():
                max_workers = pool_stats.get("max_workers", 1)
                active_threads = pool_stats.get("active_threads", 0)
                utilization = (
                    (active_threads / max_workers) * 100 if max_workers > 0 else 0
                )
                if utilization > 90:
                    overloaded_pools.append(f"{pool_name} ({utilization:.1f}%)")

            if overloaded_pools:
                status = HealthStatus.DEGRADED
                message = f"Thread pools surchargés: {', '.join(overloaded_pools)}"
            else:
                status = HealthStatus.HEALTHY
                message = "Thread pools fonctionnent normalement"

            return HealthCheckResult(
                name="thread_pools",
                status=status,
                message=message,
                details=details,
                duration_ms=(time.time() - start_time) * 1000,
            )

        except Exception as e:
            return HealthCheckResult(
                name="thread_pools",
                status=HealthStatus.UNKNOWN,
                message=f"Erreur lors de la vérification des thread pools: {str(e)}",
                duration_ms=(time.time() - start_time) * 1000,
            )

    async def _check_confluence_api(
        self, config: ConfluenceConfig
    ) -> HealthCheckResult:
        """Vérifie la connectivité avec l'API Confluence."""
        start_time = time.time()
        # Définir timeout avant le bloc try pour éviter les références avant assignation
        timeout = min(self.config.timeout_seconds, 5)

        try:
            # Import local pour éviter les dépendances circulaires
            from .client import ConfluenceClient

            # Créer un client temporaire pour le test
            client = ConfluenceClient(config)

            # Test simple de connectivité - effectuer une recherche CQL basique
            try:
                # Utiliser un timeout court pour le health check
                # Test de base - recherche CQL simple pour vérifier la connectivité
                async with asyncio.timeout(timeout):
                    # Recherche simple avec limite très faible pour le health check
                    from .config import SearchCriteria

                    test_criteria = SearchCriteria(max_results=1)
                    results = await client.search_content(test_criteria)

                await client.close()

                details = {
                    "server_url": str(config.url),
                    "api_accessible": True,
                    "test_search_results": len(results),
                    "response_time_ms": (time.time() - start_time) * 1000,
                }

                return HealthCheckResult(
                    name="confluence_api",
                    status=HealthStatus.HEALTHY,
                    message="API Confluence accessible",
                    details=details,
                    duration_ms=(time.time() - start_time) * 1000,
                )

            except asyncio.TimeoutError:
                await client.close()
                return HealthCheckResult(
                    name="confluence_api",
                    status=HealthStatus.UNHEALTHY,
                    message=f"Timeout lors de la connexion à Confluence (>{timeout}s)",
                    duration_ms=(time.time() - start_time) * 1000,
                )

            except CircuitOpenError:
                await client.close()
                return HealthCheckResult(
                    name="confluence_api",
                    status=HealthStatus.DEGRADED,
                    message="Circuit breaker ouvert pour l'API Confluence",
                    duration_ms=(time.time() - start_time) * 1000,
                )

        except Exception as e:
            return HealthCheckResult(
                name="confluence_api",
                status=HealthStatus.UNHEALTHY,
                message=f"Erreur de connexion à Confluence: {str(e)}",
                duration_ms=(time.time() - start_time) * 1000,
            )

    async def _check_storage(
        self, storage_provider: StorageProvider
    ) -> HealthCheckResult:
        """Vérifie l'état du fournisseur de stockage."""
        start_time = time.time()

        try:
            # Test d'écriture/lecture simple
            test_content = {
                "test": True,
                "timestamp": datetime.now().isoformat(),
                "health_check": "storage_test",
            }

            test_id = f"health_check_{int(time.time())}"

            # Test d'écriture
            try:
                await asyncio.wait_for(
                    storage_provider.save_content(test_id, test_content),
                    timeout=self.config.timeout_seconds,
                )
            except asyncio.TimeoutError:
                return HealthCheckResult(
                    name="storage",
                    status=HealthStatus.UNHEALTHY,
                    message=f"Timeout lors de l'écriture dans le stockage (>{self.config.timeout_seconds}s)",
                    duration_ms=(time.time() - start_time) * 1000,
                )

            # Test de lecture
            try:
                retrieved_content = await asyncio.wait_for(
                    storage_provider.get_content(test_id),
                    timeout=self.config.timeout_seconds,
                )

                if retrieved_content and retrieved_content.get("test"):
                    # Nettoyage du fichier de test
                    try:
                        await storage_provider.delete_content(test_id)
                    except Exception:
                        pass  # Ignorer les erreurs de nettoyage

                    return HealthCheckResult(
                        name="storage",
                        status=HealthStatus.HEALTHY,
                        message="Stockage accessible en lecture/écriture",
                        details={"test_id": test_id},
                        duration_ms=(time.time() - start_time) * 1000,
                    )
                else:
                    return HealthCheckResult(
                        name="storage",
                        status=HealthStatus.DEGRADED,
                        message="Écriture réussie mais lecture incohérente",
                        duration_ms=(time.time() - start_time) * 1000,
                    )

            except asyncio.TimeoutError:
                return HealthCheckResult(
                    name="storage",
                    status=HealthStatus.UNHEALTHY,
                    message=f"Timeout lors de la lecture du stockage (>{self.config.timeout_seconds}s)",
                    duration_ms=(time.time() - start_time) * 1000,
                )

        except Exception as e:
            return HealthCheckResult(
                name="storage",
                status=HealthStatus.UNHEALTHY,
                message=f"Erreur lors du test de stockage: {str(e)}",
                duration_ms=(time.time() - start_time) * 1000,
            )

    async def _check_circuit_breakers(
        self, circuit_breaker: CircuitBreaker
    ) -> HealthCheckResult:
        """Vérifie l'état des circuit breakers."""
        start_time = time.time()

        try:
            stats = circuit_breaker.get_stats()
            state = circuit_breaker.get_state()

            details = {
                "state": state.value,
                "stats": stats,
                "services": circuit_breaker.stats,
            }

            # Analyser l'état
            if state == CircuitState.OPEN:
                status = HealthStatus.DEGRADED
                message = "Circuit breaker ouvert - Services protégés indisponibles"
            elif state == CircuitState.HALF_OPEN:
                status = HealthStatus.DEGRADED
                message = "Circuit breaker en test - Récupération en cours"
            else:  # CLOSED
                # Vérifier les taux d'erreur récents
                total_failures = sum(
                    service_stats.get("failure_count", 0)
                    for service_stats in circuit_breaker.stats.values()
                )
                total_successes = sum(
                    service_stats.get("success_count", 0)
                    for service_stats in circuit_breaker.stats.values()
                )

                if total_failures + total_successes > 0:
                    error_rate = (
                        total_failures / (total_failures + total_successes)
                    ) * 100
                    details["error_rate_percent"] = round(error_rate, 2)

                    if error_rate > self.config.error_rate_threshold_percent:
                        status = HealthStatus.DEGRADED
                        message = f"Taux d'erreur élevé: {error_rate:.1f}%"
                    else:
                        status = HealthStatus.HEALTHY
                        message = (
                            f"Circuit breaker fermé - Taux d'erreur: {error_rate:.1f}%"
                        )
                else:
                    status = HealthStatus.HEALTHY
                    message = "Circuit breaker fermé - Aucune activité récente"

            return HealthCheckResult(
                name="circuit_breakers",
                status=status,
                message=message,
                details=details,
                duration_ms=(time.time() - start_time) * 1000,
            )

        except Exception as e:
            return HealthCheckResult(
                name="circuit_breakers",
                status=HealthStatus.UNKNOWN,
                message=f"Erreur lors de la vérification des circuit breakers: {str(e)}",
                duration_ms=(time.time() - start_time) * 1000,
            )

    def _determine_overall_status(
        self, checks: List[HealthCheckResult]
    ) -> HealthStatus:
        """Détermine le statut global basé sur les résultats des checks individuels."""
        if not checks:
            return HealthStatus.UNKNOWN

        statuses = [check.status for check in checks]

        # Si au moins un check est UNHEALTHY, le système est UNHEALTHY
        if HealthStatus.UNHEALTHY in statuses:
            return HealthStatus.UNHEALTHY

        # Si au moins un check est DEGRADED, le système est DEGRADED
        if HealthStatus.DEGRADED in statuses:
            return HealthStatus.DEGRADED

        # Si au moins un check est UNKNOWN, le système est UNKNOWN
        if HealthStatus.UNKNOWN in statuses:
            return HealthStatus.UNKNOWN

        # Sinon, tous les checks sont HEALTHY
        return HealthStatus.HEALTHY

    def get_cached_report(self) -> Optional[SystemHealthReport]:
        """Retourne le dernier rapport en cache s'il est encore valide."""
        if (
            self._cached_report
            and self._last_check_time
            and datetime.now() - self._last_check_time
            < timedelta(seconds=self.config.check_interval_seconds)
        ):
            return self._cached_report
        return None
