#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configuration pytest pour les tests Confluence.
Importe les fixtures depuis test_real_confluence_integration.
"""

import os
import sys

# Ajouter le chemin du projet au PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
src_path = os.path.join(project_root, "src")
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# Importer les fixtures depuis test_real_confluence_integration
from .test_real_confluence_integration import (
    confluence_config,
    real_container,
    real_env_vars,
    real_config_with_secret,
)

# Rendre les fixtures disponibles pour tous les tests dans ce répertoire
__all__ = [
    "confluence_config",
    "real_container", 
    "real_env_vars",
    "real_config_with_secret",
]
