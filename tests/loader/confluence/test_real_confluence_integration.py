#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests d'intégration avec une vraie instance Confluence.
Ces tests nécessitent une configuration réelle et sont optionnels.

Pour exécuter ces tests :
1. Configurez les variables d'environnement (voir .env.example)
2. Assurez-vous d'avoir accès à une instance Confluence de test
3. Lancez avec : pytest tests/loader/confluence/test_real_confluence_integration.py -v

Variables d'environnement requises :
- CONFLUENCE_URL : URL de votre instance Confluence
- CONFLUENCE_PAT_TOKEN : Personal Access Token
- CONFLUENCE_TEST_SPACE : Espace de test (optionnel, défaut: TEST)
"""

import json
import os
import sys
import tempfile

import pytest
from dotenv import load_dotenv
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

from src.kbotloadscheduler.dependency.container import Container

# Ajouter le chemin du projet au PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Charger les variables d'environnement
load_dotenv()


def pytest_configure(config):
    """Configuration pytest pour marquer les tests d'intégration."""
    config.addinivalue_line(
        "markers",
        "integration: marque les tests d'intégration avec vraie instance Confluence",
    )


def check_confluence_config():
    """Vérifie si la configuration Confluence est disponible."""
    url = os.getenv("CONFLUENCE_URL")
    pat_token = os.getenv("CONFLUENCE_PAT_TOKEN")

    if not url or not pat_token:
        return (
            False,
            "Variables d'environnement manquantes: CONFLUENCE_URL et CONFLUENCE_PAT_TOKEN",
        )

    return True, None


@pytest.fixture(scope="session")
def confluence_config():
    """Configuration Confluence pour les tests d'intégration."""
    is_available, error_msg = check_confluence_config()

    if not is_available:
        pytest.skip(f"Configuration Confluence non disponible: {error_msg}")

    return {
        "url": os.getenv("CONFLUENCE_URL"),
        "pat_token": os.getenv("CONFLUENCE_PAT_TOKEN"),
        "test_space": os.getenv("CONFLUENCE_TEST_SPACE", "TEST"),
        "timeout": int(os.getenv("CONFLUENCE_TIMEOUT", "30")),
    }


@pytest.fixture
def real_config_with_secret(confluence_config):
    """ConfigWithSecret configuré pour utiliser les vraies credentials."""
    from dependency_injector import providers
    from unittest.mock import MagicMock

    # Créer une configuration de test qui utilise le système de fichiers local
    config = providers.Configuration()
    config.env.from_value("integration")  # Pas 'tests' pour éviter les mocks
    config.gcp_project_id.from_value("")
    config.path_to_secret_config.from_value(
        "conf/etc/secrets/tests"
    )  # Chemin vers nos secrets de test

    # Créer l'instance ConfigWithSecret
    config_with_secret = ConfigWithSecret(config=config)

    # Pour les tests d'intégration, on peut soit :
    # 1. Utiliser les secrets du système de fichiers (recommandé)
    # 2. Ou mocker avec les vraies valeurs depuis .env

    # Option 1 : Utiliser le système de fichiers (par défaut)
    # Le ConfigWithSecret va automatiquement chercher dans conf/etc/secrets/tests/

    # Option 2 : Fallback avec mock si les fichiers n'existent pas
    try:
        # Tester si le secret existe
        test_credentials = config_with_secret.get_confluence_credentials(
            "test-perimeter"
        )
        if not test_credentials or not test_credentials.get("pat_token"):
            raise Exception("No credentials found in file system")
    except Exception:
        # Fallback : mocker avec les valeurs de .env
        config_with_secret.get_confluence_credentials = MagicMock(
            return_value={"pat_token": confluence_config["pat_token"]}
        )

    # Mocker les autres méthodes pour éviter les erreurs
    config_with_secret.get_basic_client_id = MagicMock(
        return_value="fake-basic-client-id"
    )
    config_with_secret.get_basic_client_secret = MagicMock(
        return_value="fake-basic-client-secret"
    )
    config_with_secret.get_kbot_loadscheduler_client_id = MagicMock(
        return_value="fake-kbot-client-id"
    )
    config_with_secret.get_kbot_loadscheduler_client_secret = MagicMock(
        return_value="fake-kbot-client-secret"
    )
    config_with_secret.get_sharepoint_client_config = MagicMock(
        return_value={"client_id": "fake-sharepoint-id"}
    )
    config_with_secret.get_sharepoint_client_private_key = MagicMock(
        return_value="fake-sharepoint-key"
    )

    return config_with_secret


@pytest.fixture
def real_env_vars(confluence_config):
    """Variables d'environnement pour les tests avec vraie instance."""
    original_env = os.environ.copy()

    os.environ["CONFLUENCE_URL"] = confluence_config["url"]
    os.environ["DEFAULT_SPACE_KEY"] = confluence_config["test_space"]

    yield

    # Restaurer l'environnement original
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def real_container(real_config_with_secret, real_env_vars):
    """Container configuré pour les tests d'intégration."""
    from unittest.mock import MagicMock, patch

    container = Container()

    # Remplacer le provider configWithSecret par notre configuration réelle
    container.configWithSecret.override(real_config_with_secret)

    # Mocker le BasicLoader pour éviter les erreurs
    with patch(
        "kbotloadscheduler.loader.basic.basic_loader.ProcedureSheetClient"
    ) as mock_client:
        mock_instance = MagicMock()
        mock_client.return_value = mock_instance
        yield container


@pytest.fixture
def test_source_real(confluence_config):
    """Source de test pour les tests d'intégration."""
    source_config = {
        "spaces": [confluence_config["test_space"]],
        "max_results": 5,  # Limiter pour les tests
        "include_attachments": True,
        "content_types": ["page"],
        "last_modified_days": 365,  # Récupérer du contenu récent
    }

    source = SourceBean(
        id=1,
        code="test_confluence_real",
        label="Test Confluence Real",
        src_type="confluence",
        configuration=json.dumps(source_config),
        last_load_time=0,  # Forcer la récupération
        load_interval=24,
        domain_code="test-domain",
        perimeter_code="test-perimeter",
    )

    return source


@pytest.mark.integration
class TestRealConfluenceIntegration:
    """Tests d'intégration avec une vraie instance Confluence."""

    def test_confluence_connection(
        self, real_container, confluence_config, real_env_vars
    ):
        """Teste la connexion à l'instance Confluence réelle."""
        # Obtenir le loader manager
        loader_manager = real_container.loader_manager()

        # Récupérer le loader Confluence
        confluence_loader = loader_manager.get_loader("confluence")
        assert confluence_loader is not None, "Le loader Confluence n'a pas été trouvé"
        assert isinstance(
            confluence_loader, ConfluenceLoader
        ), "Le loader n'est pas une instance de ConfluenceLoader"

        print(
            f"✅ Connexion au loader Confluence réussie pour {confluence_config['url']}"
        )

    def test_get_document_list_real(
        self, real_container, test_source_real, real_env_vars
    ):
        """Teste la récupération de documents depuis une vraie instance Confluence."""
        # Obtenir le loader manager
        loader_manager = real_container.loader_manager()
        confluence_loader = loader_manager.get_loader("confluence")

        # Récupérer la liste des documents
        documents = confluence_loader.get_document_list(test_source_real)

        # Vérifications
        assert isinstance(documents, list), "La liste des documents doit être une liste"

        if len(documents) > 0:
            print(f"✅ {len(documents)} documents récupérés")

            # Vérifier le format des documents
            for i, doc in enumerate(documents[:3]):  # Vérifier les 3 premiers
                assert isinstance(
                    doc, DocumentBean
                ), f"Document {i} n'est pas une instance de DocumentBean"
                assert doc.id is not None and doc.id != "", f"Document {i} a un ID vide"
                assert (
                    doc.name is not None and doc.name != ""
                ), f"Document {i} a un nom vide"
                assert (
                    doc.path is not None and doc.path != ""
                ), f"Document {i} a un chemin vide"
                print(f"  📄 {doc.name} (ID: {doc.id})")
        else:
            print(
                f"⚠️  Aucun document trouvé dans l'espace {test_source_real.configuration}"
            )

    @pytest.mark.slow
    def test_get_document_real(self, real_container, test_source_real, real_env_vars):
        """Teste la récupération d'un document spécifique depuis une vraie instance."""
        from unittest.mock import patch

        # Obtenir le loader manager
        loader_manager = real_container.loader_manager()
        confluence_loader = loader_manager.get_loader("confluence")

        # Récupérer la liste des documents
        documents = confluence_loader.get_document_list(test_source_real)

        if len(documents) == 0:
            pytest.skip("Aucun document disponible pour le test")

        # Prendre le premier document
        document = documents[0]
        print(f"📄 Test de récupération du document: {document.name}")

        # Créer un répertoire temporaire pour la sortie
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = f"{temp_dir}/confluence-test"

            # Mocker GCS pour éviter les appels réels
            with patch("google.cloud.storage.Client") as mock_gcs:
                from tests.testutils.mock_gcs import MockGCSClient

                mock_gcs.return_value = MockGCSClient()

                # Récupérer le document
                metadata = confluence_loader.get_document(
                    test_source_real, document, output_path
                )

                # Vérifications
                assert metadata is not None, "Aucune métadonnée retournée"
                assert "document_id" in metadata, "document_id manquant"
                assert (
                    metadata["document_id"] == document.id
                ), "ID du document incorrect"
                assert "source_type" in metadata, "source_type manquant"
                assert (
                    metadata["source_type"] == "confluence"
                ), "Type de source incorrect"

                print("✅ Document récupéré avec succès")
                print("   Métadonnées: {list(metadata.keys())}")

    def test_health_check_real(self, real_container, confluence_config, real_env_vars):
        """Teste le health check avec une vraie instance Confluence."""
        # Ce test vérifie que la connexion fonctionne
        loader_manager = real_container.loader_manager()
        confluence_loader = loader_manager.get_loader("confluence")

        # Créer une source minimale pour tester la connexion
        minimal_source = SourceBean(
            id=999,
            code="health_check",
            label="Health Check",
            src_type="confluence",
            configuration=json.dumps(
                {"spaces": [confluence_config["test_space"]], "max_results": 1}
            ),
            last_load_time=0,
            load_interval=24,
            domain_code="health",
            perimeter_code="test",
        )

        try:
            # Essayer de récupérer au moins un document
            documents = confluence_loader.get_document_list(minimal_source)
            # Ajouter une assertion pour utiliser la variable documents
            assert isinstance(documents, list), "Le résultat devrait être une liste"
            print("✅ Health check réussi - Instance Confluence accessible")
            print("   URL: {confluence_config['url']}")
            print("   Espace de test: {confluence_config['test_space']}")
            print("   Documents trouvés: {len(documents)}")
        except Exception as e:
            pytest.fail(f"Health check échoué: {str(e)}")


if __name__ == "__main__":
    # Exécuter les tests d'intégration
    pytest.main(["-xvs", __file__, "-m", "integration", "--tb=short"])
